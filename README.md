# Generador Automático de Contenido para Videos

Este proyecto utiliza la API de OpenAI (GPT-4o-mini) para generar automáticamente temas populares, guiones de videos de YouTube y evaluarlos, almacenando todo en una base de datos PostgreSQL local.

## 🚀 Características

- **Generación de temas populares**: Utiliza IA para generar temas virales y de interés común
- **Selección inteligente**: Filtra y selecciona los mejores temas automáticamente
- **Creación de guiones**: Genera guiones detallados para videos de YouTube
- **Sistema de evaluación**: Evalúa cada guión con un puntaje del 0 al 100
- **Base de datos**: Almacena todos los datos incluyendo guiones descartados
- **Migraciones**: Sistema de migraciones para gestionar la base de datos

## 📋 Requisitos Previos

1. **Node.js** (versión 18 o superior)
2. **PostgreSQL** (versión 12 o superior) ejecutándose en localhost:5432
3. **Cuenta de OpenAI** con acceso a la API

## ⚙️ Configuración

### 1. Configurar Variables de Entorno

Edita el archivo `.env` con tus credenciales:

```env
# OpenAI Configuration
OPENAI_API_KEY=tu_clave_de_openai_aqui

# PostgreSQL Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=automate_db
DB_USER=tu_usuario_db
DB_PASSWORD=tu_contraseña_db

# Application Configuration
MIN_SCRIPT_SCORE=40
```

### 2. Crear Base de Datos

Crea la base de datos en PostgreSQL:

```sql
CREATE DATABASE automate_db;
```

### 3. Ejecutar Migraciones

```bash
npm run migrate
```

## 🎯 Uso

### Ejecutar el generador completo:

```bash
npm start
```

### Scripts disponibles:

- `npm start` - Ejecuta el generador completo
- `npm run dev` - Ejecuta en modo desarrollo con auto-reload
- `npm run migrate` - Ejecuta migraciones pendientes
- `npm run migrate:down` - Revierte la última migración
- `npm run migrate:create nombre` - Crea una nueva migración

## 📊 Proceso de Generación

1. **Generación de temas**: Crea 10 temas populares para videos
2. **Selección**: Filtra y selecciona los 2-3 mejores temas
3. **Creación de guiones**: Genera guiones detallados para cada tema seleccionado
4. **Evaluación**: Evalúa cada guión en 5 criterios (0-100 puntos)
5. **Almacenamiento**: Guarda todo en la base de datos, incluso guiones rechazados

## 🗃️ Estructura de Base de Datos

### Tabla `topics`
- Almacena temas generados con puntajes de popularidad
- Marca cuáles fueron seleccionados

### Tabla `scripts`
- Almacena guiones generados para cada tema
- Incluye puntaje de evaluación y estado de aprobación

### Tabla `evaluations`
- Almacena evaluaciones detalladas de cada guión
- Incluye feedback específico por criterio

## 🔧 Personalización

- **Puntaje mínimo**: Modifica `MIN_SCRIPT_SCORE` en `.env`
- **Modelo de IA**: Cambia `gpt-4o-mini` por otro modelo en `index.js`
- **Criterios de evaluación**: Modifica los prompts en el método `evaluateScript`

## 📝 Próximas Mejoras

- Sistema de recursividad para regenerar guiones rechazados
- Integración con APIs de tendencias de YouTube
- Generación de thumbnails automáticos
- Análisis de competencia
- Programación automática de publicaciones
