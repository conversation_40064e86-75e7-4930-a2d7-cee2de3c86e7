import dotenv from 'dotenv';
import OpenAIService from './src/services/openai.js';
import DatabaseService from './src/services/database.js';
import MockDatabaseService from './src/services/mockDatabase.js';
import logger from './src/services/logger.js';

// Cargar variables de entorno
dotenv.config();

// Configurar MSW y servicios mock si está habilitado
let mswTeardown = null;
let DatabaseServiceClass = DatabaseService;

if (process.env.USE_MSW === 'true') {
  const { setupMSW, teardownMSW } = await import('./src/mocks/server.js');
  setupMSW();
  logger.info('MSW (Mock Service Worker) habilitado para pruebas');
  mswTeardown = teardownMSW;
  DatabaseServiceClass = MockDatabaseService;
  logger.info('🎭 Modo TEST activado - Usando servicios mock');
}

// Constantes
const MIN_SCRIPT_SCORE = parseInt(process.env.MIN_SCRIPT_SCORE) || 40;

class VideoContentGenerator {
  constructor() {
    this.openaiService = new OpenAIService();
    this.databaseService = new DatabaseServiceClass();
  }

  async generatePopularTopics() {
    logger.progress('Generando temas populares...');

    try {
      const topicsData = await this.openaiService.generatePopularTopics();

      // Guardar temas en la base de datos
      const savedTopics = [];
      for (const topic of topicsData.topics) {
        const savedTopic = await this.databaseService.saveTopic(topic);
        savedTopics.push(savedTopic);
      }

      logger.success(`${savedTopics.length} temas generados y guardados`);
      return savedTopics;
    } catch (error) {
      logger.failure('Error generando temas', { error: error.message });
      throw error;
    }
  }

  async selectBestTopics(topics) {
    logger.progress('Seleccionando los mejores temas...');

    try {
      const response = await this.openaiService.selectBestTopics(topics);

      const selectedNumbers = response
        .split(',')
        .map(num => parseInt(num.trim()) - 1);

      const selectedTopics = selectedNumbers.map(index => topics[index]).filter(Boolean);

      // Marcar temas como seleccionados en la base de datos
      const topicIds = selectedTopics.map(topic => topic.id);
      await this.databaseService.markTopicsAsSelected(topicIds);

      logger.success(`${selectedTopics.length} temas seleccionados`);
      return selectedTopics;
    } catch (error) {
      logger.failure('Error seleccionando temas', { error: error.message });
      throw error;
    }
  }

  async generateScript(topic) {
    logger.progress(`Generando guión para: ${topic.title}`);

    try {
      const scriptContent = await this.openaiService.generateScript(topic);

      // Guardar guión en la base de datos
      const savedScript = await this.databaseService.saveScript(
        topic.id,
        `Guión: ${topic.title}`,
        scriptContent
      );

      logger.success(`Guión generado para: ${topic.title}`);
      return savedScript;
    } catch (error) {
      logger.failure('Error generando guión', {
        error: error.message,
        topicTitle: topic.title
      });
      throw error;
    }
  }

  async evaluateScript(script) {
    logger.progress(`Evaluando guión: ${script.title}`);

    try {
      const evaluationData = await this.openaiService.evaluateScript(script);

      // Actualizar score del guión
      await this.databaseService.updateScriptScore(
        script.id,
        evaluationData.total_score,
        evaluationData.total_score >= MIN_SCRIPT_SCORE
      );

      // Guardar evaluación detallada
      await this.databaseService.saveEvaluation(
        script.id,
        'comprehensive',
        evaluationData.total_score,
        evaluationData.feedback
      );

      logger.success(`Guión evaluado - Score: ${evaluationData.total_score}/100`);
      return evaluationData;
    } catch (error) {
      logger.failure('Error evaluando guión', {
        error: error.message,
        scriptTitle: script.title
      });
      throw error;
    }
  }

  async run() {
    logger.start('Iniciando generador de contenido para videos...');

    try {
      // 1. Generar temas populares
      const topics = await this.generatePopularTopics();

      // 2. Seleccionar los mejores temas
      const selectedTopics = await this.selectBestTopics(topics);

      // 3. Generar guiones para cada tema seleccionado
      const scripts = [];
      for (const topic of selectedTopics) {
        const script = await this.generateScript(topic);
        scripts.push(script);
      }

      // 4. Evaluar cada guión
      const evaluations = [];
      for (const script of scripts) {
        const evaluation = await this.evaluateScript(script);
        evaluations.push({ script, evaluation });
      }

      // 5. Mostrar resultados
      logger.info('\n📊 RESULTADOS FINALES:');
      logger.info('='.repeat(50));

      evaluations.forEach(({ script, evaluation }, index) => {
        const status = evaluation.total_score >= MIN_SCRIPT_SCORE ? '✅ APROBADO' : '❌ RECHAZADO';
        logger.info(`\n${index + 1}. ${script.title}`);
        logger.info(`   Score: ${evaluation.total_score}/100 - ${status}`);
        logger.info(`   Feedback: ${evaluation.feedback.substring(0, 100)}...`);
      });

      const approvedScripts = evaluations.filter(({ evaluation }) =>
        evaluation.total_score >= MIN_SCRIPT_SCORE
      );

      logger.success(`Resumen: ${approvedScripts.length}/${scripts.length} guiones aprobados`);

      if (approvedScripts.length === 0) {
        logger.warn('Ningún guión alcanzó el puntaje mínimo. Considera ajustar los criterios o generar nuevos temas.');
      }

      // Mostrar estadísticas finales
      const stats = await this.databaseService.getStats();
      logger.info('Estadísticas finales:', stats);

    } catch (error) {
      logger.failure('Error en el proceso', { error: error.message });
      throw error;
    } finally {
      await this.databaseService.close();
    }
  }
}

// Función principal
async function main() {
  const mode = process.env.USE_MSW === 'true' ? 'MODO TEST' : 'MODO PRODUCCIÓN';
  logger.start(`Iniciando aplicación en ${mode}...`);

  try {
    const generator = new VideoContentGenerator();
    await generator.run();
  } catch (error) {
    logger.failure('Error fatal', { error: error.message, stack: error.stack });
    throw error;
  } finally {
    // Limpiar MSW si estaba activo
    if (mswTeardown) {
      mswTeardown();
    }
  }
}

// Ejecutar directamente
main().catch(error => {
  process.exit(1);
});