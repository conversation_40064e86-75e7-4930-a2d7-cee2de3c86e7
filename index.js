import dotenv from 'dotenv';
import OpenAI from 'openai';
import pkg from 'pg';
const { Pool } = pkg;

// Cargar variables de entorno
dotenv.config();

// Configuración de OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Configuración de PostgreSQL
const pool = new Pool({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
});

// Constantes
const MIN_SCRIPT_SCORE = parseInt(process.env.MIN_SCRIPT_SCORE) || 40;

class VideoContentGenerator {
  constructor() {
    this.openai = openai;
    this.db = pool;
  }

  async generatePopularTopics() {
    console.log('🎯 Generando temas populares...');

    const prompt = `
    Genera una lista de 10 temas populares para videos de YouTube que tengan mucho alcance y sean de interés común para mucha gente.
    Los temas deben ser actuales, virales o de tendencia.

    Responde en formato JSON con la siguiente estructura:
    {
      "topics": [
        {
          "title": "Título del tema",
          "description": "Descripción breve del tema",
          "popularity_score": número del 1 al 100
        }
      ]
    }
    `;

    try {
      const response = await this.openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.8,
      });

      const content = response.choices[0].message.content;
      const topicsData = JSON.parse(content);

      // Guardar temas en la base de datos
      const savedTopics = [];
      for (const topic of topicsData.topics) {
        const result = await this.db.query(
          'INSERT INTO topics (title, description, popularity_score) VALUES ($1, $2, $3) RETURNING *',
          [topic.title, topic.description, topic.popularity_score]
        );
        savedTopics.push(result.rows[0]);
      }

      console.log(`✅ ${savedTopics.length} temas generados y guardados`);
      return savedTopics;
    } catch (error) {
      console.error('❌ Error generando temas:', error);
      throw error;
    }
  }

  async selectBestTopics(topics) {
    console.log('🔍 Seleccionando los mejores temas...');

    const topicsText = topics.map((topic, index) =>
      `${index + 1}. ${topic.title} - ${topic.description} (Score: ${topic.popularity_score})`
    ).join('\n');

    const prompt = `
    De la siguiente lista de temas para videos de YouTube, selecciona los 2-3 mejores basándote en:
    - Potencial viral
    - Interés general del público
    - Facilidad para crear contenido atractivo

    Lista de temas:
    ${topicsText}

    Responde solo con los números de los temas seleccionados separados por comas (ej: 1,3,7)
    `;

    try {
      const response = await this.openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.3,
      });

      const selectedNumbers = response.choices[0].message.content
        .split(',')
        .map(num => parseInt(num.trim()) - 1);

      const selectedTopics = selectedNumbers.map(index => topics[index]).filter(Boolean);

      // Marcar temas como seleccionados en la base de datos
      for (const topic of selectedTopics) {
        await this.db.query(
          'UPDATE topics SET is_selected = true WHERE id = $1',
          [topic.id]
        );
      }

      console.log(`✅ ${selectedTopics.length} temas seleccionados`);
      return selectedTopics;
    } catch (error) {
      console.error('❌ Error seleccionando temas:', error);
      throw error;
    }
  }

  async generateScript(topic) {
    console.log(`📝 Generando guión para: ${topic.title}`);

    const prompt = `
    Crea un guión detallado para un video de YouTube sobre el tema: "${topic.title}"

    Descripción del tema: ${topic.description}

    El guión debe incluir:
    - Hook inicial atractivo (primeros 15 segundos)
    - Introducción clara del tema
    - 3-5 puntos principales bien desarrollados
    - Llamadas a la acción durante el video
    - Cierre memorable con call-to-action

    El guión debe ser para un video de 8-12 minutos aproximadamente.
    Usa un tono conversacional y atractivo para YouTube.
    `;

    try {
      const response = await this.openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.7,
      });

      const scriptContent = response.choices[0].message.content;

      // Guardar guión en la base de datos
      const result = await this.db.query(
        'INSERT INTO scripts (topic_id, title, content) VALUES ($1, $2, $3) RETURNING *',
        [topic.id, `Guión: ${topic.title}`, scriptContent]
      );

      console.log(`✅ Guión generado para: ${topic.title}`);
      return result.rows[0];
    } catch (error) {
      console.error('❌ Error generando guión:', error);
      throw error;
    }
  }

  async evaluateScript(script) {
    console.log(`🔍 Evaluando guión: ${script.title}`);

    const prompt = `
    Evalúa el siguiente guión de video de YouTube en una escala del 0 al 100 basándote en:

    1. Hook inicial (0-20 puntos): ¿Qué tan atractivos son los primeros 15 segundos?
    2. Estructura (0-20 puntos): ¿Está bien organizado y fluye correctamente?
    3. Engagement (0-20 puntos): ¿Mantiene el interés durante todo el video?
    4. Call-to-actions (0-20 puntos): ¿Incluye CTAs efectivos?
    5. Potencial viral (0-20 puntos): ¿Tiene elementos que pueden hacerlo viral?

    Guión a evaluar:
    ${script.content}

    Responde en formato JSON:
    {
      "total_score": número del 0 al 100,
      "hook_score": número del 0 al 20,
      "structure_score": número del 0 al 20,
      "engagement_score": número del 0 al 20,
      "cta_score": número del 0 al 20,
      "viral_score": número del 0 al 20,
      "feedback": "comentarios detallados sobre fortalezas y debilidades"
    }
    `;

    try {
      const response = await this.openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.3,
      });

      const evaluationData = JSON.parse(response.choices[0].message.content);

      // Actualizar score del guión
      await this.db.query(
        'UPDATE scripts SET score = $1, is_approved = $2 WHERE id = $3',
        [evaluationData.total_score, evaluationData.total_score >= MIN_SCRIPT_SCORE, script.id]
      );

      // Guardar evaluación detallada
      await this.db.query(
        'INSERT INTO evaluations (script_id, criteria, score, feedback) VALUES ($1, $2, $3, $4)',
        [script.id, 'comprehensive', evaluationData.total_score, evaluationData.feedback]
      );

      console.log(`✅ Guión evaluado - Score: ${evaluationData.total_score}/100`);
      return evaluationData;
    } catch (error) {
      console.error('❌ Error evaluando guión:', error);
      throw error;
    }
  }

  async run() {
    console.log('🚀 Iniciando generador de contenido para videos...\n');

    try {
      // 1. Generar temas populares
      const topics = await this.generatePopularTopics();
      console.log('\n');

      // 2. Seleccionar los mejores temas
      const selectedTopics = await this.selectBestTopics(topics);
      console.log('\n');

      // 3. Generar guiones para cada tema seleccionado
      const scripts = [];
      for (const topic of selectedTopics) {
        const script = await this.generateScript(topic);
        scripts.push(script);
      }
      console.log('\n');

      // 4. Evaluar cada guión
      const evaluations = [];
      for (const script of scripts) {
        const evaluation = await this.evaluateScript(script);
        evaluations.push({ script, evaluation });
      }

      // 5. Mostrar resultados
      console.log('\n📊 RESULTADOS FINALES:');
      console.log('=' .repeat(50));

      evaluations.forEach(({ script, evaluation }, index) => {
        const status = evaluation.total_score >= MIN_SCRIPT_SCORE ? '✅ APROBADO' : '❌ RECHAZADO';
        console.log(`\n${index + 1}. ${script.title}`);
        console.log(`   Score: ${evaluation.total_score}/100 - ${status}`);
        console.log(`   Feedback: ${evaluation.feedback.substring(0, 100)}...`);
      });

      const approvedScripts = evaluations.filter(({ evaluation }) =>
        evaluation.total_score >= MIN_SCRIPT_SCORE
      );

      console.log(`\n🎯 Resumen: ${approvedScripts.length}/${scripts.length} guiones aprobados`);

      if (approvedScripts.length === 0) {
        console.log('⚠️  Ningún guión alcanzó el puntaje mínimo. Considera ajustar los criterios o generar nuevos temas.');
      }

    } catch (error) {
      console.error('💥 Error en el proceso:', error);
    } finally {
      await this.db.end();
    }
  }
}

// Función principal
async function main() {
  const generator = new VideoContentGenerator();
  await generator.run();
}

// Ejecutar si es el archivo principal
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}