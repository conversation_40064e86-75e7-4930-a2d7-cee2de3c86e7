/**
 * @type {import('node-pg-migrate').ColumnDefinitions | undefined}
 */
export const shorthands = undefined;

/**
 * @param pgm {import('node-pg-migrate').MigrationBuilder}
 * @param run {() => void | undefined}
 * @returns {Promise<void> | void}
 */
export const up = (pgm) => {
  pgm.createTable('scripts', {
    id: 'id',
    topic_id: {
      type: 'integer',
      notNull: true,
      references: '"topics"',
      onDelete: 'cascade',
    },
    title: { type: 'varchar(255)', notNull: true },
    content: { type: 'text', notNull: true },
    score: { type: 'integer' },
    is_approved: { type: 'boolean', default: false },
    created_at: {
      type: 'timestamp',
      notNull: true,
      default: pgm.func('current_timestamp'),
    },
  });
};

/**
 * @param pgm {import('node-pg-migrate').MigrationBuilder}
 * @param run {() => void | undefined}
 * @returns {Promise<void> | void}
 */
export const down = (pgm) => {
  pgm.dropTable('scripts');
};
