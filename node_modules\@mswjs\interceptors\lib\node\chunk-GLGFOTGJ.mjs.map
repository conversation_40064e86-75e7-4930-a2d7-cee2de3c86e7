{"version": 3, "sources": ["../../src/interceptors/ClientRequest/index.ts", "../../src/interceptors/ClientRequest/MockHttpSocket.ts", "../../src/interceptors/Socket/MockSocket.ts", "../../src/interceptors/Socket/utils/normalizeSocketWriteArgs.ts", "../../src/interceptors/Socket/utils/baseUrlFromConnectionOptions.ts", "../../src/interceptors/ClientRequest/utils/recordRawHeaders.ts", "../../src/interceptors/ClientRequest/agents.ts", "../../src/interceptors/ClientRequest/utils/normalizeClientRequestArgs.ts", "../../src/utils/getUrlByRequestOptions.ts", "../../src/utils/cloneObject.ts"], "sourcesContent": ["import http from 'node:http'\nimport https from 'node:https'\nimport { Interceptor } from '../../Interceptor'\nimport type { HttpRequestEventMap } from '../../glossary'\nimport {\n  kRequestId,\n  MockHttpSocketRequestCallback,\n  MockHttpSocketResponseCallback,\n} from './MockHttpSocket'\nimport { MockAgent, MockHttpsAgent } from './agents'\nimport { RequestController } from '../../RequestController'\nimport { emitAsync } from '../../utils/emitAsync'\nimport { normalizeClientRequestArgs } from './utils/normalizeClientRequestArgs'\nimport { handleRequest } from '../../utils/handleRequest'\nimport {\n  recordRawFetchHeaders,\n  restoreHeadersPrototype,\n} from './utils/recordRawHeaders'\n\nexport class ClientRequestInterceptor extends Interceptor<HttpRequestEventMap> {\n  static symbol = Symbol('client-request-interceptor')\n\n  constructor() {\n    super(ClientRequestInterceptor.symbol)\n  }\n\n  protected setup(): void {\n    const {\n      ClientRequest: OriginalClientRequest,\n      get: originalGet,\n      request: originalRequest,\n    } = http\n    const { get: originalHttpsGet, request: originalHttpsRequest } = https\n\n    const onRequest = this.onRequest.bind(this)\n    const onResponse = this.onResponse.bind(this)\n\n    // Support requests performed via the `ClientRequest` constructor directly.\n    http.ClientRequest = new Proxy(http.ClientRequest, {\n      construct: (target, args: Parameters<typeof http.request>) => {\n        const [url, options, callback] = normalizeClientRequestArgs(\n          'http:',\n          args\n        )\n\n        // Create a mock agent instance appropriate for the request protocol.\n        const Agent = options.protocol === 'https:' ? MockHttpsAgent : MockAgent\n        const mockAgent = new Agent({\n          customAgent: options.agent,\n          onRequest,\n          onResponse,\n        })\n        options.agent = mockAgent\n\n        return Reflect.construct(target, [url, options, callback])\n      },\n    })\n\n    http.request = new Proxy(http.request, {\n      apply: (target, thisArg, args: Parameters<typeof http.request>) => {\n        const [url, options, callback] = normalizeClientRequestArgs(\n          'http:',\n          args\n        )\n        const mockAgent = new MockAgent({\n          customAgent: options.agent,\n          onRequest,\n          onResponse,\n        })\n        options.agent = mockAgent\n\n        return Reflect.apply(target, thisArg, [url, options, callback])\n      },\n    })\n\n    http.get = new Proxy(http.get, {\n      apply: (target, thisArg, args: Parameters<typeof http.get>) => {\n        const [url, options, callback] = normalizeClientRequestArgs(\n          'http:',\n          args\n        )\n\n        const mockAgent = new MockAgent({\n          customAgent: options.agent,\n          onRequest,\n          onResponse,\n        })\n        options.agent = mockAgent\n\n        return Reflect.apply(target, thisArg, [url, options, callback])\n      },\n    })\n\n    //\n    // HTTPS.\n    //\n\n    https.request = new Proxy(https.request, {\n      apply: (target, thisArg, args: Parameters<typeof https.request>) => {\n        const [url, options, callback] = normalizeClientRequestArgs(\n          'https:',\n          args\n        )\n\n        const mockAgent = new MockHttpsAgent({\n          customAgent: options.agent,\n          onRequest,\n          onResponse,\n        })\n        options.agent = mockAgent\n\n        return Reflect.apply(target, thisArg, [url, options, callback])\n      },\n    })\n\n    https.get = new Proxy(https.get, {\n      apply: (target, thisArg, args: Parameters<typeof https.get>) => {\n        const [url, options, callback] = normalizeClientRequestArgs(\n          'https:',\n          args\n        )\n\n        const mockAgent = new MockHttpsAgent({\n          customAgent: options.agent,\n          onRequest,\n          onResponse,\n        })\n        options.agent = mockAgent\n\n        return Reflect.apply(target, thisArg, [url, options, callback])\n      },\n    })\n\n    // Spy on `Header.prototype.set` and `Header.prototype.append` calls\n    // and record the raw header names provided. This is to support\n    // `IncomingMessage.prototype.rawHeaders`.\n    recordRawFetchHeaders()\n\n    this.subscriptions.push(() => {\n      http.ClientRequest = OriginalClientRequest\n\n      http.get = originalGet\n      http.request = originalRequest\n\n      https.get = originalHttpsGet\n      https.request = originalHttpsRequest\n\n      restoreHeadersPrototype()\n    })\n  }\n\n  private onRequest: MockHttpSocketRequestCallback = async ({\n    request,\n    socket,\n  }) => {\n    const requestId = Reflect.get(request, kRequestId)\n    const controller = new RequestController(request)\n\n    const isRequestHandled = await handleRequest({\n      request,\n      requestId,\n      controller,\n      emitter: this.emitter,\n      onResponse: (response) => {\n        socket.respondWith(response)\n      },\n      onRequestError: (response) => {\n        socket.respondWith(response)\n      },\n      onError: (error) => {\n        if (error instanceof Error) {\n          socket.errorWith(error)\n        }\n      },\n    })\n\n    if (!isRequestHandled) {\n      return socket.passthrough()\n    }\n  }\n\n  public onResponse: MockHttpSocketResponseCallback = async ({\n    requestId,\n    request,\n    response,\n    isMockedResponse,\n  }) => {\n    // Return the promise to when all the response event listeners\n    // are finished.\n    return emitAsync(this.emitter, 'response', {\n      requestId,\n      request,\n      response,\n      isMockedResponse,\n    })\n  }\n}\n", "import net from 'node:net'\nimport {\n  H<PERSON><PERSON>arser,\n  type RequestHeadersCompleteCallback,\n  type ResponseHeadersCompleteCallback,\n} from '_http_common'\nimport { STATUS_CODES, IncomingMessage, ServerResponse } from 'node:http'\nimport { Readable } from 'node:stream'\nimport { invariant } from 'outvariant'\nimport { INTERNAL_REQUEST_ID_HEADER_NAME } from '../../Interceptor'\nimport { MockSocket } from '../Socket/MockSocket'\nimport type { NormalizedSocketWriteArgs } from '../Socket/utils/normalizeSocketWriteArgs'\nimport { isPropertyAccessible } from '../../utils/isPropertyAccessible'\nimport { baseUrlFromConnectionOptions } from '../Socket/utils/baseUrlFromConnectionOptions'\nimport { createServerErrorResponse } from '../../utils/responseUtils'\nimport { createRequestId } from '../../createRequestId'\nimport { getRawFetchHeaders } from './utils/recordRawHeaders'\nimport { FetchResponse } from '../../utils/fetchUtils'\nimport { setRawRequest } from '../../getRawRequest'\nimport { setRawRequestBodyStream } from '../../utils/node'\n\ntype HttpConnectionOptions = any\n\nexport type MockHttpSocketRequestCallback = (args: {\n  requestId: string\n  request: Request\n  socket: MockHttpSocket\n}) => void\n\nexport type MockHttpSocketResponseCallback = (args: {\n  requestId: string\n  request: Request\n  response: Response\n  isMockedResponse: boolean\n  socket: MockHttpSocket\n}) => Promise<void>\n\ninterface MockHttpSocketOptions {\n  connectionOptions: HttpConnectionOptions\n  createConnection: () => net.Socket\n  onRequest: MockHttpSocketRequestCallback\n  onResponse: MockHttpSocketResponseCallback\n}\n\nexport const kRequestId = Symbol('kRequestId')\n\nexport class MockHttpSocket extends MockSocket {\n  private connectionOptions: HttpConnectionOptions\n  private createConnection: () => net.Socket\n  private baseUrl: URL\n\n  private onRequest: MockHttpSocketRequestCallback\n  private onResponse: MockHttpSocketResponseCallback\n  private responseListenersPromise?: Promise<void>\n\n  private writeBuffer: Array<NormalizedSocketWriteArgs> = []\n  private request?: Request\n  private requestParser: HTTPParser<0>\n  private requestStream?: Readable\n  private shouldKeepAlive?: boolean\n\n  private socketState: 'unknown' | 'mock' | 'passthrough' = 'unknown'\n  private responseParser: HTTPParser<1>\n  private responseStream?: Readable\n  private originalSocket?: net.Socket\n\n  constructor(options: MockHttpSocketOptions) {\n    super({\n      write: (chunk, encoding, callback) => {\n        // Buffer the writes so they can be flushed in case of the original connection\n        // and when reading the request body in the interceptor. If the connection has\n        // been established, no need to buffer the chunks anymore, they will be forwarded.\n        if (this.socketState !== 'passthrough') {\n          this.writeBuffer.push([chunk, encoding, callback])\n        }\n\n        if (chunk) {\n          /**\n           * Forward any writes to the mock socket to the underlying original socket.\n           * This ensures functional duplex connections, like WebSocket.\n           * @see https://github.com/mswjs/interceptors/issues/682\n           */\n          if (this.socketState === 'passthrough') {\n            this.originalSocket?.write(chunk, encoding, callback)\n          }\n\n          this.requestParser.execute(\n            Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk, encoding)\n          )\n        }\n      },\n      read: (chunk) => {\n        if (chunk !== null) {\n          /**\n           * @todo We need to free the parser if the connection has been\n           * upgraded to a non-HTTP protocol. It won't be able to parse data\n           * from that point onward anyway. No need to keep it in memory.\n           */\n          this.responseParser.execute(\n            Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk)\n          )\n        }\n      },\n    })\n\n    this.connectionOptions = options.connectionOptions\n    this.createConnection = options.createConnection\n    this.onRequest = options.onRequest\n    this.onResponse = options.onResponse\n\n    this.baseUrl = baseUrlFromConnectionOptions(this.connectionOptions)\n\n    // Request parser.\n    this.requestParser = new HTTPParser()\n    this.requestParser.initialize(HTTPParser.REQUEST, {})\n    this.requestParser[HTTPParser.kOnHeadersComplete] =\n      this.onRequestStart.bind(this)\n    this.requestParser[HTTPParser.kOnBody] = this.onRequestBody.bind(this)\n    this.requestParser[HTTPParser.kOnMessageComplete] =\n      this.onRequestEnd.bind(this)\n\n    // Response parser.\n    this.responseParser = new HTTPParser()\n    this.responseParser.initialize(HTTPParser.RESPONSE, {})\n    this.responseParser[HTTPParser.kOnHeadersComplete] =\n      this.onResponseStart.bind(this)\n    this.responseParser[HTTPParser.kOnBody] = this.onResponseBody.bind(this)\n    this.responseParser[HTTPParser.kOnMessageComplete] =\n      this.onResponseEnd.bind(this)\n\n    // Once the socket is finished, nothing can write to it\n    // anymore. It has also flushed any buffered chunks.\n    this.once('finish', () => this.requestParser.free())\n\n    if (this.baseUrl.protocol === 'https:') {\n      Reflect.set(this, 'encrypted', true)\n      // The server certificate is not the same as a CA\n      // passed to the TLS socket connection options.\n      Reflect.set(this, 'authorized', false)\n      Reflect.set(this, 'getProtocol', () => 'TLSv1.3')\n      Reflect.set(this, 'getSession', () => undefined)\n      Reflect.set(this, 'isSessionReused', () => false)\n    }\n  }\n\n  public emit(event: string | symbol, ...args: any[]): boolean {\n    const emitEvent = super.emit.bind(this, event as any, ...args)\n\n    if (this.responseListenersPromise) {\n      this.responseListenersPromise.finally(emitEvent)\n      return this.listenerCount(event) > 0\n    }\n\n    return emitEvent()\n  }\n\n  public destroy(error?: Error | undefined): this {\n    // Destroy the response parser when the socket gets destroyed.\n    // Normally, we shoud listen to the \"close\" event but it\n    // can be suppressed by using the \"emitClose: false\" option.\n    this.responseParser.free()\n\n    if (error) {\n      this.emit('error', error)\n    }\n\n    return super.destroy(error)\n  }\n\n  /**\n   * Establish this Socket connection as-is and pipe\n   * its data/events through this Socket.\n   */\n  public passthrough(): void {\n    this.socketState = 'passthrough'\n\n    if (this.destroyed) {\n      return\n    }\n\n    const socket = this.createConnection()\n    this.originalSocket = socket\n\n    /**\n     * @note Inherit the original socket's connection handle.\n     * Without this, each push to the mock socket results in a\n     * new \"connection\" listener being added (i.e. buffering pushes).\n     * @see https://github.com/nodejs/node/blob/b18153598b25485ce4f54d0c5cb830a9457691ee/lib/net.js#L734\n     */\n    if ('_handle' in socket) {\n      Object.defineProperty(this, '_handle', {\n        value: socket._handle,\n        enumerable: true,\n        writable: true,\n      })\n    }\n\n    // If the developer destroys the socket, destroy the original connection.\n    this.once('error', (error) => {\n      socket.destroy(error)\n    })\n\n    this.address = socket.address.bind(socket)\n\n    // Flush the buffered \"socket.write()\" calls onto\n    // the original socket instance (i.e. write request body).\n    // Exhaust the \"requestBuffer\" in case this Socket\n    // gets reused for different requests.\n    let writeArgs: NormalizedSocketWriteArgs | undefined\n    let headersWritten = false\n\n    while ((writeArgs = this.writeBuffer.shift())) {\n      if (writeArgs !== undefined) {\n        if (!headersWritten) {\n          const [chunk, encoding, callback] = writeArgs\n          const chunkString = chunk.toString()\n          const chunkBeforeRequestHeaders = chunkString.slice(\n            0,\n            chunkString.indexOf('\\r\\n') + 2\n          )\n          const chunkAfterRequestHeaders = chunkString.slice(\n            chunk.indexOf('\\r\\n\\r\\n')\n          )\n          const rawRequestHeaders = getRawFetchHeaders(this.request!.headers)\n          const requestHeadersString = rawRequestHeaders\n            // Skip the internal request ID deduplication header.\n            .filter(([name]) => {\n              return name.toLowerCase() !== INTERNAL_REQUEST_ID_HEADER_NAME\n            })\n            .map(([name, value]) => `${name}: ${value}`)\n            .join('\\r\\n')\n\n          // Modify the HTTP request message headers\n          // to reflect any changes to the request headers\n          // from the \"request\" event listener.\n          const headersChunk = `${chunkBeforeRequestHeaders}${requestHeadersString}${chunkAfterRequestHeaders}`\n          socket.write(headersChunk, encoding, callback)\n          headersWritten = true\n          continue\n        }\n\n        socket.write(...writeArgs)\n      }\n    }\n\n    // Forward TLS Socket properties onto this Socket instance\n    // in the case of a TLS/SSL connection.\n    if (Reflect.get(socket, 'encrypted')) {\n      const tlsProperties = [\n        'encrypted',\n        'authorized',\n        'getProtocol',\n        'getSession',\n        'isSessionReused',\n      ]\n\n      tlsProperties.forEach((propertyName) => {\n        Object.defineProperty(this, propertyName, {\n          enumerable: true,\n          get: () => {\n            const value = Reflect.get(socket, propertyName)\n            return typeof value === 'function' ? value.bind(socket) : value\n          },\n        })\n      })\n    }\n\n    socket\n      .on('lookup', (...args) => this.emit('lookup', ...args))\n      .on('connect', () => {\n        this.connecting = socket.connecting\n        this.emit('connect')\n      })\n      .on('secureConnect', () => this.emit('secureConnect'))\n      .on('secure', () => this.emit('secure'))\n      .on('session', (session) => this.emit('session', session))\n      .on('ready', () => this.emit('ready'))\n      .on('drain', () => this.emit('drain'))\n      .on('data', (chunk) => {\n        // Push the original response to this socket\n        // so it triggers the HTTP response parser. This unifies\n        // the handling pipeline for original and mocked response.\n        this.push(chunk)\n      })\n      .on('error', (error) => {\n        Reflect.set(this, '_hadError', Reflect.get(socket, '_hadError'))\n        this.emit('error', error)\n      })\n      .on('resume', () => this.emit('resume'))\n      .on('timeout', () => this.emit('timeout'))\n      .on('prefinish', () => this.emit('prefinish'))\n      .on('finish', () => this.emit('finish'))\n      .on('close', (hadError) => this.emit('close', hadError))\n      .on('end', () => this.emit('end'))\n  }\n\n  /**\n   * Convert the given Fetch API `Response` instance to an\n   * HTTP message and push it to the socket.\n   */\n  public async respondWith(response: Response): Promise<void> {\n    // Ignore the mocked response if the socket has been destroyed\n    // (e.g. aborted or timed out),\n    if (this.destroyed) {\n      return\n    }\n\n    // Handle \"type: error\" responses.\n    if (isPropertyAccessible(response, 'type') && response.type === 'error') {\n      this.errorWith(new TypeError('Network error'))\n      return\n    }\n\n    // First, emit all the connection events\n    // to emulate a successful connection.\n    this.mockConnect()\n    this.socketState = 'mock'\n\n    // Flush the write buffer to trigger write callbacks\n    // if it hasn't been flushed already (e.g. someone started reading request stream).\n    this.flushWriteBuffer()\n\n    // Create a `ServerResponse` instance to delegate HTTP message parsing,\n    // Transfer-Encoding, and other things to Node.js internals.\n    const serverResponse = new ServerResponse(new IncomingMessage(this))\n\n    /**\n     * Assign a mock socket instance to the server response to\n     * spy on the response chunk writes. Push the transformed response chunks\n     * to this `MockHttpSocket` instance to trigger the \"data\" event.\n     * @note Providing the same `MockSocket` instance when creating `ServerResponse`\n     * does not have the same effect.\n     * @see https://github.com/nodejs/node/blob/10099bb3f7fd97bb9dd9667188426866b3098e07/test/parallel/test-http-server-response-standalone.js#L32\n     */\n    serverResponse.assignSocket(\n      new MockSocket({\n        write: (chunk, encoding, callback) => {\n          this.push(chunk, encoding)\n          callback?.()\n        },\n        read() {},\n      })\n    )\n\n    /**\n     * @note Remove the `Connection` and `Date` response headers\n     * injected by `ServerResponse` by default. Those are required\n     * from the server but the interceptor is NOT technically a server.\n     * It's confusing to add response headers that the developer didn't\n     * specify themselves. They can always add these if they wish.\n     * @see https://www.rfc-editor.org/rfc/rfc9110#field.date\n     * @see https://www.rfc-editor.org/rfc/rfc9110#field.connection\n     */\n    serverResponse.removeHeader('connection')\n    serverResponse.removeHeader('date')\n\n    const rawResponseHeaders = getRawFetchHeaders(response.headers)\n\n    /**\n     * @note Call `.writeHead` in order to set the raw response headers\n     * in the same case as they were provided by the developer. Using\n     * `.setHeader()`/`.appendHeader()` normalizes header names.\n     */\n    serverResponse.writeHead(\n      response.status,\n      response.statusText || STATUS_CODES[response.status],\n      rawResponseHeaders\n    )\n\n    // If the developer destroy the socket, gracefully destroy the response.\n    this.once('error', () => {\n      serverResponse.destroy()\n    })\n\n    if (response.body) {\n      try {\n        const reader = response.body.getReader()\n\n        while (true) {\n          const { done, value } = await reader.read()\n\n          if (done) {\n            serverResponse.end()\n            break\n          }\n\n          serverResponse.write(value)\n        }\n      } catch (error) {\n        // Coerce response stream errors to 500 responses.\n        this.respondWith(createServerErrorResponse(error))\n        return\n      }\n    } else {\n      serverResponse.end()\n    }\n\n    // Close the socket if the connection wasn't marked as keep-alive.\n    if (!this.shouldKeepAlive) {\n      this.emit('readable')\n\n      /**\n       * @todo @fixme This is likely a hack.\n       * Since we push null to the socket, it never propagates to the\n       * parser, and the parser never calls \"onResponseEnd\" to close\n       * the response stream. We are closing the stream here manually\n       * but that shouldn't be the case.\n       */\n      this.responseStream?.push(null)\n      this.push(null)\n    }\n  }\n\n  /**\n   * Close this socket connection with the given error.\n   */\n  public errorWith(error?: Error): void {\n    this.destroy(error)\n  }\n\n  private mockConnect(): void {\n    // Calling this method immediately puts the socket\n    // into the connected state.\n    this.connecting = false\n\n    const isIPv6 =\n      net.isIPv6(this.connectionOptions.hostname) ||\n      this.connectionOptions.family === 6\n    const addressInfo = {\n      address: isIPv6 ? '::1' : '127.0.0.1',\n      family: isIPv6 ? 'IPv6' : 'IPv4',\n      port: this.connectionOptions.port,\n    }\n    // Return fake address information for the socket.\n    this.address = () => addressInfo\n    this.emit(\n      'lookup',\n      null,\n      addressInfo.address,\n      addressInfo.family === 'IPv6' ? 6 : 4,\n      this.connectionOptions.host\n    )\n    this.emit('connect')\n    this.emit('ready')\n\n    if (this.baseUrl.protocol === 'https:') {\n      this.emit('secure')\n      this.emit('secureConnect')\n\n      // A single TLS connection is represented by two \"session\" events.\n      this.emit(\n        'session',\n        this.connectionOptions.session ||\n          Buffer.from('mock-session-renegotiate')\n      )\n      this.emit('session', Buffer.from('mock-session-resume'))\n    }\n  }\n\n  private flushWriteBuffer(): void {\n    for (const writeCall of this.writeBuffer) {\n      if (typeof writeCall[2] === 'function') {\n        writeCall[2]()\n        /**\n         * @note Remove the callback from the write call\n         * so it doesn't get called twice on passthrough\n         * if `request.end()` was called within `request.write()`.\n         * @see https://github.com/mswjs/interceptors/issues/684\n         */\n        writeCall[2] = undefined\n      }\n    }\n  }\n\n  private onRequestStart: RequestHeadersCompleteCallback = (\n    versionMajor,\n    versionMinor,\n    rawHeaders,\n    _,\n    path,\n    __,\n    ___,\n    ____,\n    shouldKeepAlive\n  ) => {\n    this.shouldKeepAlive = shouldKeepAlive\n\n    const url = new URL(path, this.baseUrl)\n    const method = this.connectionOptions.method?.toUpperCase() || 'GET'\n    const headers = FetchResponse.parseRawHeaders(rawHeaders)\n    const canHaveBody = method !== 'GET' && method !== 'HEAD'\n\n    // Translate the basic authorization in the URL to the request header.\n    // Constructing a Request instance with a URL containing auth is no-op.\n    if (url.username || url.password) {\n      if (!headers.has('authorization')) {\n        headers.set('authorization', `Basic ${url.username}:${url.password}`)\n      }\n      url.username = ''\n      url.password = ''\n    }\n\n    // Create a new stream for each request.\n    // If this Socket is reused for multiple requests,\n    // this ensures that each request gets its own stream.\n    // One Socket instance can only handle one request at a time.\n    this.requestStream = new Readable({\n      /**\n       * @note Provide the `read()` method so a `Readable` could be\n       * used as the actual request body (the stream calls \"read()\").\n       * We control the queue in the onRequestBody/End functions.\n       */\n      read: () => {\n        // If the user attempts to read the request body,\n        // flush the write buffer to trigger the callbacks.\n        // This way, if the request stream ends in the write callback,\n        // it will indeed end correctly.\n        this.flushWriteBuffer()\n      },\n    })\n\n    const requestId = createRequestId()\n    this.request = new Request(url, {\n      method,\n      headers,\n      credentials: 'same-origin',\n      // @ts-expect-error Undocumented Fetch property.\n      duplex: canHaveBody ? 'half' : undefined,\n      body: canHaveBody ? (Readable.toWeb(this.requestStream!) as any) : null,\n    })\n\n    Reflect.set(this.request, kRequestId, requestId)\n\n    // Set the raw `http.ClientRequest` instance on the request instance.\n    // This is useful for cases like getting the raw headers of the request.\n    setRawRequest(this.request, Reflect.get(this, '_httpMessage'))\n\n    // Create a copy of the request body stream and store it on the request.\n    // This is only needed for the consumers who wish to read the request body stream\n    // of requests that cannot have a body per Fetch API specification (i.e. GET, HEAD).\n    setRawRequestBodyStream(this.request, this.requestStream)\n\n    // Skip handling the request that's already being handled\n    // by another (parent) interceptor. For example, XMLHttpRequest\n    // is often implemented via ClientRequest in Node.js (e.g. JSDOM).\n    // In that case, XHR interceptor will bubble down to the ClientRequest\n    // interceptor. No need to try to handle that request again.\n    /**\n     * @fixme Stop relying on the \"X-Request-Id\" request header\n     * to figure out if one interceptor has been invoked within another.\n     * @see https://github.com/mswjs/interceptors/issues/378\n     */\n    if (this.request.headers.has(INTERNAL_REQUEST_ID_HEADER_NAME)) {\n      this.passthrough()\n      return\n    }\n\n    this.onRequest({\n      requestId,\n      request: this.request,\n      socket: this,\n    })\n  }\n\n  private onRequestBody(chunk: Buffer): void {\n    invariant(\n      this.requestStream,\n      'Failed to write to a request stream: stream does not exist'\n    )\n\n    this.requestStream.push(chunk)\n  }\n\n  private onRequestEnd(): void {\n    // Request end can be called for requests without body.\n    if (this.requestStream) {\n      this.requestStream.push(null)\n    }\n  }\n\n  private onResponseStart: ResponseHeadersCompleteCallback = (\n    versionMajor,\n    versionMinor,\n    rawHeaders,\n    method,\n    url,\n    status,\n    statusText\n  ) => {\n    const headers = FetchResponse.parseRawHeaders(rawHeaders)\n\n    const response = new FetchResponse(\n      /**\n       * @note The Fetch API response instance exposed to the consumer\n       * is created over the response stream of the HTTP parser. It is NOT\n       * related to the Socket instance. This way, you can read response body\n       * in response listener while the Socket instance delays the emission\n       * of \"end\" and other events until those response listeners are finished.\n       */\n      FetchResponse.isResponseWithBody(status)\n        ? (Readable.toWeb(\n            (this.responseStream = new Readable({ read() {} }))\n          ) as any)\n        : null,\n      {\n        url,\n        status,\n        statusText,\n        headers,\n      }\n    )\n\n    invariant(\n      this.request,\n      'Failed to handle a response: request does not exist'\n    )\n\n    FetchResponse.setUrl(this.request.url, response)\n\n    /**\n     * @fixme Stop relying on the \"X-Request-Id\" request header\n     * to figure out if one interceptor has been invoked within another.\n     * @see https://github.com/mswjs/interceptors/issues/378\n     */\n    if (this.request.headers.has(INTERNAL_REQUEST_ID_HEADER_NAME)) {\n      return\n    }\n\n    this.responseListenersPromise = this.onResponse({\n      response,\n      isMockedResponse: this.socketState === 'mock',\n      requestId: Reflect.get(this.request, kRequestId),\n      request: this.request,\n      socket: this,\n    })\n  }\n\n  private onResponseBody(chunk: Buffer) {\n    invariant(\n      this.responseStream,\n      'Failed to write to a response stream: stream does not exist'\n    )\n\n    this.responseStream.push(chunk)\n  }\n\n  private onResponseEnd(): void {\n    // Response end can be called for responses without body.\n    if (this.responseStream) {\n      this.responseStream.push(null)\n    }\n  }\n}\n", "import net from 'node:net'\nimport {\n  normalizeSocketWriteArgs,\n  type WriteArgs,\n  type WriteCallback,\n} from './utils/normalizeSocketWriteArgs'\n\nexport interface MockSocketOptions {\n  write: (\n    chunk: Buffer | string,\n    encoding: BufferEncoding | undefined,\n    callback?: WriteCallback\n  ) => void\n\n  read: (chunk: <PERSON>uffer, encoding: BufferEncoding | undefined) => void\n}\n\nexport class MockSocket extends net.Socket {\n  public connecting: boolean\n\n  constructor(protected readonly options: MockSocketOptions) {\n    super()\n    this.connecting = false\n    this.connect()\n\n    this._final = (callback) => {\n      callback(null)\n    }\n  }\n\n  public connect() {\n    // The connection will remain pending until\n    // the consumer decides to handle it.\n    this.connecting = true\n    return this\n  }\n\n  public write(...args: Array<unknown>): boolean {\n    const [chunk, encoding, callback] = normalizeSocketWriteArgs(\n      args as WriteArgs\n    )\n    this.options.write(chunk, encoding, callback)\n    return true\n  }\n\n  public end(...args: Array<unknown>) {\n    const [chunk, encoding, callback] = normalizeSocketWriteArgs(\n      args as WriteArgs\n    )\n    this.options.write(chunk, encoding, callback)\n    return super.end.apply(this, args as any)\n  }\n\n  public push(chunk: any, encoding?: BufferEncoding): boolean {\n    this.options.read(chunk, encoding)\n    return super.push(chunk, encoding)\n  }\n}\n", "export type WriteCallback = (error?: Error | null) => void\n\nexport type WriteArgs =\n  | [chunk: unknown, callback?: WriteCallback]\n  | [chunk: unknown, encoding: BufferEncoding, callback?: WriteCallback]\n\nexport type NormalizedSocketWriteArgs = [\n  chunk: any,\n  encoding?: BufferEncoding,\n  callback?: WriteCallback,\n]\n\n/**\n * Normalizes the arguments provided to the `Writable.prototype.write()`\n * and `Writable.prototype.end()`.\n */\nexport function normalizeSocketWriteArgs(\n  args: WriteArgs\n): NormalizedSocketWriteArgs {\n  const normalized: NormalizedSocketWriteArgs = [args[0], undefined, undefined]\n\n  if (typeof args[1] === 'string') {\n    normalized[1] = args[1]\n  } else if (typeof args[1] === 'function') {\n    normalized[2] = args[1]\n  }\n\n  if (typeof args[2] === 'function') {\n    normalized[2] = args[2]\n  }\n\n  return normalized\n}\n", "export function baseUrlFromConnectionOptions(options: any): URL {\n  if ('href' in options) {\n    return new URL(options.href)\n  }\n\n  const protocol = options.port === 443 ? 'https:' : 'http:'\n  const host = options.host\n\n  const url = new URL(`${protocol}//${host}`)\n\n  if (options.port) {\n    url.port = options.port.toString()\n  }\n\n  if (options.path) {\n    url.pathname = options.path\n  }\n\n  if (options.auth) {\n    const [username, password] = options.auth.split(':')\n    url.username = username\n    url.password = password\n  }\n\n  return url\n}\n", "type HeaderTuple = [string, string]\ntype RawHeaders = Array<HeaderTuple>\ntype SetHeaderBehavior = 'set' | 'append'\n\nconst kRawHeaders = Symbol('kRawHeaders')\nconst kRestorePatches = Symbol('kRestorePatches')\n\nfunction recordRawHeader(\n  headers: Headers,\n  args: HeaderTuple,\n  behavior: SetHeaderBehavior\n) {\n  ensureRawHeadersSymbol(headers, [])\n  const rawHeaders = Reflect.get(headers, kRawHeaders) as RawHeaders\n\n  if (behavior === 'set') {\n    // When recording a set header, ensure we remove any matching existing headers.\n    for (let index = rawHeaders.length - 1; index >= 0; index--) {\n      if (rawHeaders[index][0].toLowerCase() === args[0].toLowerCase()) {\n        rawHeaders.splice(index, 1)\n      }\n    }\n  }\n\n  rawHeaders.push(args)\n}\n\n/**\n * Define the raw headers symbol on the given `Headers` instance.\n * If the symbol already exists, this function does nothing.\n */\nfunction ensureRawHeadersSymbol(\n  headers: Headers,\n  rawHeaders: RawHeaders\n): void {\n  if (Reflect.has(headers, kRawHeaders)) {\n    return\n  }\n\n  defineRawHeadersSymbol(headers, rawHeaders)\n}\n\n/**\n * Define the raw headers symbol on the given `Headers` instance.\n * If the symbol already exists, it gets overridden.\n */\nfunction defineRawHeadersSymbol(headers: Headers, rawHeaders: RawHeaders) {\n  Object.defineProperty(headers, kRawHeaders, {\n    value: rawHeaders,\n    enumerable: false,\n    // Mark the symbol as configurable so its value can be overridden.\n    // Overrides happen when merging raw headers from multiple sources.\n    // E.g. new Request(new Request(url, { headers }), { headers })\n    configurable: true,\n  })\n}\n\n/**\n * Patch the global `Headers` class to store raw headers.\n * This is for compatibility with `IncomingMessage.prototype.rawHeaders`.\n *\n * @note Node.js has their own raw headers symbol but it\n * only records the first header name in case of multi-value headers.\n * Any other headers are normalized before comparing. This makes it\n * incompatible with the `rawHeaders` format.\n *\n * let h = new Headers()\n * h.append('X-Custom', 'one')\n * h.append('x-custom', 'two')\n * h[Symbol('headers map')] // Map { 'X-Custom' => 'one, two' }\n */\nexport function recordRawFetchHeaders() {\n  // Prevent patching the Headers prototype multiple times.\n  if (Reflect.get(Headers, kRestorePatches)) {\n    return Reflect.get(Headers, kRestorePatches)\n  }\n\n  const {\n    Headers: OriginalHeaders,\n    Request: OriginalRequest,\n    Response: OriginalResponse,\n  } = globalThis\n  const { set, append, delete: headersDeleteMethod } = Headers.prototype\n\n  Object.defineProperty(Headers, kRestorePatches, {\n    value: () => {\n      Headers.prototype.set = set\n      Headers.prototype.append = append\n      Headers.prototype.delete = headersDeleteMethod\n      globalThis.Headers = OriginalHeaders\n\n      globalThis.Request = OriginalRequest\n      globalThis.Response = OriginalResponse\n\n      Reflect.deleteProperty(Headers, kRestorePatches)\n    },\n    enumerable: false,\n    /**\n     * @note Mark this property as configurable\n     * so we can delete it using `Reflect.delete` during cleanup.\n     */\n    configurable: true,\n  })\n\n  Object.defineProperty(globalThis, 'Headers', {\n    enumerable: true,\n    writable: true,\n    value: new Proxy(Headers, {\n      construct(target, args, newTarget) {\n        const headersInit = args[0] || []\n\n        if (\n          headersInit instanceof Headers &&\n          Reflect.has(headersInit, kRawHeaders)\n        ) {\n          const headers = Reflect.construct(\n            target,\n            [Reflect.get(headersInit, kRawHeaders)],\n            newTarget\n          )\n          ensureRawHeadersSymbol(headers, [\n            /**\n             * @note Spread the retrieved headers to clone them.\n             * This prevents multiple Headers instances from pointing\n             * at the same internal \"rawHeaders\" array.\n             */\n            ...Reflect.get(headersInit, kRawHeaders),\n          ])\n          return headers\n        }\n\n        const headers = Reflect.construct(target, args, newTarget)\n\n        // Request/Response constructors will set the symbol\n        // upon creating a new instance, using the raw developer\n        // input as the raw headers. Skip the symbol altogether\n        // in those cases because the input to Headers will be normalized.\n        if (!Reflect.has(headers, kRawHeaders)) {\n          const rawHeadersInit = Array.isArray(headersInit)\n            ? headersInit\n            : Object.entries(headersInit)\n          ensureRawHeadersSymbol(headers, rawHeadersInit)\n        }\n\n        return headers\n      },\n    }),\n  })\n\n  Headers.prototype.set = new Proxy(Headers.prototype.set, {\n    apply(target, thisArg, args: HeaderTuple) {\n      recordRawHeader(thisArg, args, 'set')\n      return Reflect.apply(target, thisArg, args)\n    },\n  })\n\n  Headers.prototype.append = new Proxy(Headers.prototype.append, {\n    apply(target, thisArg, args: HeaderTuple) {\n      recordRawHeader(thisArg, args, 'append')\n      return Reflect.apply(target, thisArg, args)\n    },\n  })\n\n  Headers.prototype.delete = new Proxy(Headers.prototype.delete, {\n    apply(target, thisArg, args: [string]) {\n      const rawHeaders = Reflect.get(thisArg, kRawHeaders) as RawHeaders\n\n      if (rawHeaders) {\n        for (let index = rawHeaders.length - 1; index >= 0; index--) {\n          if (rawHeaders[index][0].toLowerCase() === args[0].toLowerCase()) {\n            rawHeaders.splice(index, 1)\n          }\n        }\n      }\n\n      return Reflect.apply(target, thisArg, args)\n    },\n  })\n\n  Object.defineProperty(globalThis, 'Request', {\n    enumerable: true,\n    writable: true,\n    value: new Proxy(Request, {\n      construct(target, args, newTarget) {\n        const request = Reflect.construct(target, args, newTarget)\n        const inferredRawHeaders: RawHeaders = []\n\n        // Infer raw headers from a `Request` instance used as init.\n        if (typeof args[0] === 'object' && args[0].headers != null) {\n          inferredRawHeaders.push(...inferRawHeaders(args[0].headers))\n        }\n\n        // Infer raw headers from the \"headers\" init argument.\n        if (typeof args[1] === 'object' && args[1].headers != null) {\n          inferredRawHeaders.push(...inferRawHeaders(args[1].headers))\n        }\n\n        if (inferredRawHeaders.length > 0) {\n          ensureRawHeadersSymbol(request.headers, inferredRawHeaders)\n        }\n\n        return request\n      },\n    }),\n  })\n\n  Object.defineProperty(globalThis, 'Response', {\n    enumerable: true,\n    writable: true,\n    value: new Proxy(Response, {\n      construct(target, args, newTarget) {\n        const response = Reflect.construct(target, args, newTarget)\n\n        if (typeof args[1] === 'object' && args[1].headers != null) {\n          ensureRawHeadersSymbol(\n            response.headers,\n            inferRawHeaders(args[1].headers)\n          )\n        }\n\n        return response\n      },\n    }),\n  })\n}\n\nexport function restoreHeadersPrototype() {\n  if (!Reflect.get(Headers, kRestorePatches)) {\n    return\n  }\n\n  Reflect.get(Headers, kRestorePatches)()\n}\n\nexport function getRawFetchHeaders(headers: Headers): RawHeaders {\n  // If the raw headers recording failed for some reason,\n  // use the normalized header entries instead.\n  if (!Reflect.has(headers, kRawHeaders)) {\n    return Array.from(headers.entries())\n  }\n\n  const rawHeaders = Reflect.get(headers, kRawHeaders) as RawHeaders\n  return rawHeaders.length > 0 ? rawHeaders : Array.from(headers.entries())\n}\n\n/**\n * Infers the raw headers from the given `HeadersInit` provided\n * to the Request/Response constructor.\n *\n * If the `init.headers` is a Headers instance, use it directly.\n * That means the headers were created standalone and already have\n * the raw headers stored.\n * If the `init.headers` is a HeadersInit, create a new Headers\n * instace out of it.\n */\nfunction inferRawHeaders(headers: HeadersInit): RawHeaders {\n  if (headers instanceof Headers) {\n    return Reflect.get(headers, kRawHeaders) || []\n  }\n\n  return Reflect.get(new Headers(headers), kRawHeaders)\n}\n", "import net from 'node:net'\nimport http from 'node:http'\nimport https from 'node:https'\nimport {\n  MockHttpSocket,\n  type MockHttpSocketRequestCallback,\n  type MockHttpSocketResponseCallback,\n} from './MockHttpSocket'\n\ndeclare module 'node:http' {\n  interface Agent {\n    options?: http.AgentOptions\n    createConnection(options: any, callback: any): net.Socket\n  }\n}\n\ninterface MockAgentOptions {\n  customAgent?: http.RequestOptions['agent']\n  onRequest: MockHttpSocketRequestCallback\n  onResponse: MockHttpSocketResponseCallback\n}\n\nexport class MockAgent extends http.Agent {\n  private customAgent?: http.RequestOptions['agent']\n  private onRequest: MockHttpSocketRequestCallback\n  private onResponse: MockHttpSocketResponseCallback\n\n  constructor(options: MockAgentOptions) {\n    super()\n    this.customAgent = options.customAgent\n    this.onRequest = options.onRequest\n    this.onResponse = options.onResponse\n  }\n\n  public createConnection(options: any, callback: any): net.Socket {\n    const createConnection =\n      this.customAgent instanceof http.Agent\n        ? this.customAgent.createConnection\n        : super.createConnection\n\n    const createConnectionOptions =\n      this.customAgent instanceof http.Agent\n        ? {\n            ...options,\n            ...this.customAgent.options,\n          }\n        : options\n\n    const socket = new MockHttpSocket({\n      connectionOptions: options,\n      createConnection: createConnection.bind(\n        this.customAgent || this,\n        createConnectionOptions,\n        callback\n      ),\n      onRequest: this.onRequest.bind(this),\n      onResponse: this.onResponse.bind(this),\n    })\n\n    return socket\n  }\n}\n\nexport class MockHttpsAgent extends https.Agent {\n  private customAgent?: https.RequestOptions['agent']\n  private onRequest: MockHttpSocketRequestCallback\n  private onResponse: MockHttpSocketResponseCallback\n\n  constructor(options: MockAgentOptions) {\n    super()\n    this.customAgent = options.customAgent\n    this.onRequest = options.onRequest\n    this.onResponse = options.onResponse\n  }\n\n  public createConnection(options: any, callback: any): net.Socket {\n    const createConnection =\n      this.customAgent instanceof https.Agent\n        ? this.customAgent.createConnection\n        : super.createConnection\n\n    const createConnectionOptions =\n      this.customAgent instanceof https.Agent\n        ? {\n            ...options,\n            ...this.customAgent.options,\n          }\n        : options\n\n    const socket = new MockHttpSocket({\n      connectionOptions: options,\n      createConnection: createConnection.bind(\n        this.customAgent || this,\n        createConnectionOptions,\n        callback\n      ),\n      onRequest: this.onRequest.bind(this),\n      onResponse: this.onResponse.bind(this),\n    })\n\n    return socket\n  }\n}\n", "import { urlToHttpOptions } from 'node:url'\nimport {\n  Agent as HttpAgent,\n  globalAgent as httpGlobalAgent,\n  IncomingMessage,\n} from 'node:http'\nimport {\n  RequestOptions,\n  Agent as HttpsAgent,\n  globalAgent as httpsGlobalAgent,\n} from 'node:https'\nimport {\n  /**\n   * @note Use the Node.js URL instead of the global URL\n   * because environments like JSDOM may override the global,\n   * breaking the compatibility with Node.js.\n   * @see https://github.com/node-fetch/node-fetch/issues/1376#issuecomment-966435555\n   */\n  URL,\n  Url as LegacyURL,\n  parse as parseUrl,\n} from 'node:url'\nimport { Logger } from '@open-draft/logger'\nimport {\n  ResolvedRequestOptions,\n  getUrlByRequestOptions,\n} from '../../../utils/getUrlByRequestOptions'\nimport { cloneObject } from '../../../utils/cloneObject'\nimport { isObject } from '../../../utils/isObject'\n\nconst logger = new Logger('http normalizeClientRequestArgs')\n\nexport type HttpRequestCallback = (response: IncomingMessage) => void\n\nexport type ClientRequestArgs =\n  // Request without any arguments is also possible.\n  | []\n  | [string | URL | LegacyURL, HttpRequestCallback?]\n  | [string | URL | LegacyURL, RequestOptions, HttpRequestCallback?]\n  | [RequestOptions, HttpRequestCallback?]\n\nfunction resolveRequestOptions(\n  args: ClientRequestArgs,\n  url: URL\n): RequestOptions {\n  // Calling `fetch` provides only URL to `ClientRequest`\n  // without any `RequestOptions` or callback.\n  if (typeof args[1] === 'undefined' || typeof args[1] === 'function') {\n    logger.info('request options not provided, deriving from the url', url)\n    return urlToHttpOptions(url)\n  }\n\n  if (args[1]) {\n    logger.info('has custom RequestOptions!', args[1])\n    const requestOptionsFromUrl = urlToHttpOptions(url)\n\n    logger.info('derived RequestOptions from the URL:', requestOptionsFromUrl)\n\n    /**\n     * Clone the request options to lock their state\n     * at the moment they are provided to `ClientRequest`.\n     * @see https://github.com/mswjs/interceptors/issues/86\n     */\n    logger.info('cloning RequestOptions...')\n    const clonedRequestOptions = cloneObject(args[1])\n    logger.info('successfully cloned RequestOptions!', clonedRequestOptions)\n\n    return {\n      ...requestOptionsFromUrl,\n      ...clonedRequestOptions,\n    }\n  }\n\n  logger.info('using an empty object as request options')\n  return {} as RequestOptions\n}\n\n/**\n * Overrides the given `URL` instance with the explicit properties provided\n * on the `RequestOptions` object. The options object takes precedence,\n * and will replace URL properties like \"host\", \"path\", and \"port\", if specified.\n */\nfunction overrideUrlByRequestOptions(url: URL, options: RequestOptions): URL {\n  url.host = options.host || url.host\n  url.hostname = options.hostname || url.hostname\n  url.port = options.port ? options.port.toString() : url.port\n\n  if (options.path) {\n    const parsedOptionsPath = parseUrl(options.path, false)\n    url.pathname = parsedOptionsPath.pathname || ''\n    url.search = parsedOptionsPath.search || ''\n  }\n\n  return url\n}\n\nfunction resolveCallback(\n  args: ClientRequestArgs\n): HttpRequestCallback | undefined {\n  return typeof args[1] === 'function' ? args[1] : args[2]\n}\n\nexport type NormalizedClientRequestArgs = [\n  url: URL,\n  options: ResolvedRequestOptions,\n  callback?: HttpRequestCallback\n]\n\n/**\n * Normalizes parameters given to a `http.request` call\n * so it always has a `URL` and `RequestOptions`.\n */\nexport function normalizeClientRequestArgs(\n  defaultProtocol: string,\n  args: ClientRequestArgs\n): NormalizedClientRequestArgs {\n  let url: URL\n  let options: ResolvedRequestOptions\n  let callback: HttpRequestCallback | undefined\n\n  logger.info('arguments', args)\n  logger.info('using default protocol:', defaultProtocol)\n\n  // Support \"http.request()\" calls without any arguments.\n  // That call results in a \"GET http://localhost\" request.\n  if (args.length === 0) {\n    const url = new URL('http://localhost')\n    const options = resolveRequestOptions(args, url)\n    return [url, options]\n  }\n\n  // Convert a url string into a URL instance\n  // and derive request options from it.\n  if (typeof args[0] === 'string') {\n    logger.info('first argument is a location string:', args[0])\n\n    url = new URL(args[0])\n    logger.info('created a url:', url)\n\n    const requestOptionsFromUrl = urlToHttpOptions(url)\n    logger.info('request options from url:', requestOptionsFromUrl)\n\n    options = resolveRequestOptions(args, url)\n    logger.info('resolved request options:', options)\n\n    callback = resolveCallback(args)\n  }\n  // Handle a given URL instance as-is\n  // and derive request options from it.\n  else if (args[0] instanceof URL) {\n    url = args[0]\n    logger.info('first argument is a URL:', url)\n\n    // Check if the second provided argument is RequestOptions.\n    // If it is, check if \"options.path\" was set and rewrite it\n    // on the input URL.\n    // Do this before resolving options from the URL below\n    // to prevent query string from being duplicated in the path.\n    if (typeof args[1] !== 'undefined' && isObject<RequestOptions>(args[1])) {\n      url = overrideUrlByRequestOptions(url, args[1])\n    }\n\n    options = resolveRequestOptions(args, url)\n    logger.info('derived request options:', options)\n\n    callback = resolveCallback(args)\n  }\n  // Handle a legacy URL instance and re-normalize from either a RequestOptions object\n  // or a WHATWG URL.\n  else if ('hash' in args[0] && !('method' in args[0])) {\n    const [legacyUrl] = args\n    logger.info('first argument is a legacy URL:', legacyUrl)\n\n    if (legacyUrl.hostname === null) {\n      /**\n       * We are dealing with a relative url, so use the path as an \"option\" and\n       * merge in any existing options, giving priority to exising options -- i.e. a path in any\n       * existing options will take precedence over the one contained in the url. This is consistent\n       * with the behaviour in ClientRequest.\n       * @see https://github.com/nodejs/node/blob/d84f1312915fe45fe0febe888db692c74894c382/lib/_http_client.js#L122\n       */\n      logger.info('given legacy URL is relative (no hostname)')\n\n      return isObject(args[1])\n        ? normalizeClientRequestArgs(defaultProtocol, [\n            { path: legacyUrl.path, ...args[1] },\n            args[2],\n          ])\n        : normalizeClientRequestArgs(defaultProtocol, [\n            { path: legacyUrl.path },\n            args[1] as HttpRequestCallback,\n          ])\n    }\n\n    logger.info('given legacy url is absolute')\n\n    // We are dealing with an absolute URL, so convert to WHATWG and try again.\n    const resolvedUrl = new URL(legacyUrl.href)\n\n    return args[1] === undefined\n      ? normalizeClientRequestArgs(defaultProtocol, [resolvedUrl])\n      : typeof args[1] === 'function'\n      ? normalizeClientRequestArgs(defaultProtocol, [resolvedUrl, args[1]])\n      : normalizeClientRequestArgs(defaultProtocol, [\n          resolvedUrl,\n          args[1],\n          args[2],\n        ])\n  }\n  // Handle a given \"RequestOptions\" object as-is\n  // and derive the URL instance from it.\n  else if (isObject(args[0])) {\n    options = { ...(args[0] as any) }\n    logger.info('first argument is RequestOptions:', options)\n\n    // When handling a \"RequestOptions\" object without an explicit \"protocol\",\n    // infer the protocol from the request issuing module (http/https).\n    options.protocol = options.protocol || defaultProtocol\n    logger.info('normalized request options:', options)\n\n    url = getUrlByRequestOptions(options)\n    logger.info('created a URL from RequestOptions:', url.href)\n\n    callback = resolveCallback(args)\n  } else {\n    throw new Error(\n      `Failed to construct ClientRequest with these parameters: ${args}`\n    )\n  }\n\n  options.protocol = options.protocol || url.protocol\n  options.method = options.method || 'GET'\n\n  /**\n   * Infer a fallback agent from the URL protocol.\n   * The interception is done on the \"ClientRequest\" level (\"NodeClientRequest\")\n   * and it may miss the correct agent. Always align the agent\n   * with the URL protocol, if not provided.\n   *\n   * @note Respect the \"agent: false\" value.\n   */\n  if (typeof options.agent === 'undefined') {\n    const agent =\n      options.protocol === 'https:'\n        ? new HttpsAgent({\n            // Any other value other than false is considered as true, so we don't add this property if undefined.\n            ...('rejectUnauthorized' in options && {\n              rejectUnauthorized: options.rejectUnauthorized,\n            }),\n          })\n        : new HttpAgent()\n\n    options.agent = agent\n    logger.info('resolved fallback agent:', agent)\n  }\n\n  /**\n   * Ensure that the default Agent is always set.\n   * This prevents the protocol mismatch for requests with { agent: false },\n   * where the global Agent is inferred.\n   * @see https://github.com/mswjs/msw/issues/1150\n   * @see https://github.com/nodejs/node/blob/418ff70b810f0e7112d48baaa72932a56cfa213b/lib/_http_client.js#L130\n   * @see https://github.com/nodejs/node/blob/418ff70b810f0e7112d48baaa72932a56cfa213b/lib/_http_client.js#L157-L159\n   */\n  if (!options._defaultAgent) {\n    logger.info(\n      'has no default agent, setting the default agent for \"%s\"',\n      options.protocol\n    )\n\n    options._defaultAgent =\n      options.protocol === 'https:' ? httpsGlobalAgent : httpGlobalAgent\n  }\n\n  logger.info('successfully resolved url:', url.href)\n  logger.info('successfully resolved options:', options)\n  logger.info('successfully resolved callback:', callback)\n\n  /**\n   * @note If the user-provided URL is not a valid URL in Node.js,\n   * (e.g. the one provided by the JSDOM polyfills), case it to\n   * string. Otherwise, this throws on Node.js incompatibility\n   * (`ERR_INVALID_ARG_TYPE` on the connection listener)\n   * @see https://github.com/node-fetch/node-fetch/issues/1376#issuecomment-966435555\n   */\n  if (!(url instanceof URL)) {\n    url = (url as any).toString()\n  }\n\n  return [url, options, callback]\n}\n", "import { Agent } from 'http'\nimport { RequestOptions, Agent as HttpsAgent } from 'https'\nimport { Logger } from '@open-draft/logger'\n\nconst logger = new Logger('utils getUrlByRequestOptions')\n\n// Request instance constructed by the \"request\" library\n// has a \"self\" property that has a \"uri\" field. This is\n// reproducible by performing a \"XMLHttpRequest\" request in JSDOM.\nexport interface RequestSelf {\n  uri?: URL\n}\n\nexport type ResolvedRequestOptions = RequestOptions & RequestSelf\n\nexport const DEFAULT_PATH = '/'\nconst DEFAULT_PROTOCOL = 'http:'\nconst DEFAULT_HOSTNAME = 'localhost'\nconst SSL_PORT = 443\n\nfunction getAgent(\n  options: ResolvedRequestOptions\n): Agent | HttpsAgent | undefined {\n  return options.agent instanceof Agent ? options.agent : undefined\n}\n\nfunction getProtocolByRequestOptions(options: ResolvedRequestOptions): string {\n  if (options.protocol) {\n    return options.protocol\n  }\n\n  const agent = getAgent(options)\n  const agentProtocol = (agent as RequestOptions)?.protocol\n\n  if (agentProtocol) {\n    return agentProtocol\n  }\n\n  const port = getPortByRequestOptions(options)\n  const isSecureRequest = options.cert || port === SSL_PORT\n\n  return isSecureRequest ? 'https:' : options.uri?.protocol || DEFAULT_PROTOCOL\n}\n\nfunction getPortByRequestOptions(\n  options: ResolvedRequestOptions\n): number | undefined {\n  // Use the explicitly provided port.\n  if (options.port) {\n    return Number(options.port)\n  }\n\n  // Otherwise, try to resolve port from the agent.\n  const agent = getAgent(options)\n\n  if ((agent as HttpsAgent)?.options.port) {\n    return Number((agent as HttpsAgent).options.port)\n  }\n\n  if ((agent as RequestOptions)?.defaultPort) {\n    return Number((agent as RequestOptions).defaultPort)\n  }\n\n  // Lastly, return undefined indicating that the port\n  // must inferred from the protocol. Do not infer it here.\n  return undefined\n}\n\ninterface RequestAuth {\n  username: string\n  password: string\n}\n\nfunction getAuthByRequestOptions(\n  options: ResolvedRequestOptions\n): RequestAuth | undefined {\n  if (options.auth) {\n    const [username, password] = options.auth.split(':')\n    return { username, password }\n  }\n}\n\n/**\n * Returns true if host looks like an IPv6 address without surrounding brackets\n * It assumes any host containing `:` is definitely not IPv4 and probably IPv6,\n * but note that this could include invalid IPv6 addresses as well.\n */\nfunction isRawIPv6Address(host: string): boolean {\n  return host.includes(':') && !host.startsWith('[') && !host.endsWith(']')\n}\n\nfunction getHostname(options: ResolvedRequestOptions): string | undefined {\n  let host = options.hostname || options.host\n\n  if (host) {\n    if (isRawIPv6Address(host)) {\n       host = `[${host}]`\n    }\n\n    // Check the presence of the port, and if it's present,\n    // remove it from the host, returning a hostname.\n    return new URL(`http://${host}`).hostname\n  }\n\n  return DEFAULT_HOSTNAME\n}\n\n/**\n * Creates a `URL` instance from a given `RequestOptions` object.\n */\nexport function getUrlByRequestOptions(options: ResolvedRequestOptions): URL {\n  logger.info('request options', options)\n\n  if (options.uri) {\n    logger.info(\n      'constructing url from explicitly provided \"options.uri\": %s',\n      options.uri\n    )\n    return new URL(options.uri.href)\n  }\n\n  logger.info('figuring out url from request options...')\n\n  const protocol = getProtocolByRequestOptions(options)\n  logger.info('protocol', protocol)\n\n  const port = getPortByRequestOptions(options)\n  logger.info('port', port)\n\n  const hostname = getHostname(options)\n  logger.info('hostname', hostname)\n\n  const path = options.path || DEFAULT_PATH\n  logger.info('path', path)\n\n  const credentials = getAuthByRequestOptions(options)\n  logger.info('credentials', credentials)\n\n  const authString = credentials\n    ? `${credentials.username}:${credentials.password}@`\n    : ''\n  logger.info('auth string:', authString)\n\n  const portString = typeof port !== 'undefined' ? `:${port}` : ''\n  const url = new URL(`${protocol}//${hostname}${portString}${path}`)\n  url.username = credentials?.username || ''\n  url.password = credentials?.password || ''\n\n  logger.info('created url:', url)\n\n  return url\n}\n", "import { Logger } from '@open-draft/logger'\n\nconst logger = new Logger('cloneObject')\n\nfunction isPlainObject(obj?: Record<string, any>): boolean {\n  logger.info('is plain object?', obj)\n\n  if (obj == null || !obj.constructor?.name) {\n    logger.info('given object is undefined, not a plain object...')\n    return false\n  }\n\n  logger.info('checking the object constructor:', obj.constructor.name)\n  return obj.constructor.name === 'Object'\n}\n\nexport function cloneObject<ObjectType extends Record<string, any>>(\n  obj: ObjectType\n): ObjectType {\n  logger.info('cloning object:', obj)\n\n  const enumerableProperties = Object.entries(obj).reduce<Record<string, any>>(\n    (acc, [key, value]) => {\n      logger.info('analyzing key-value pair:', key, value)\n\n      // Recursively clone only plain objects, omitting class instances.\n      acc[key] = isPlainObject(value) ? cloneObject(value) : value\n      return acc\n    },\n    {}\n  )\n\n  return isPlainObject(obj)\n    ? enumerableProperties\n    : Object.assign(Object.getPrototypeOf(obj), enumerableProperties)\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,WAAU;AACjB,OAAOC,YAAW;;;ACDlB,OAAOC,UAAS;AAChB;AAAA,EACE;AAAA,OAGK;AACP,SAAS,cAAc,iBAAiB,sBAAsB;AAC9D,SAAS,gBAAgB;AACzB,SAAS,iBAAiB;;;ACR1B,OAAO,SAAS;;;ACgBT,SAAS,yBACd,MAC2B;AAC3B,QAAM,aAAwC,CAAC,KAAK,CAAC,GAAG,QAAW,MAAS;AAE5E,MAAI,OAAO,KAAK,CAAC,MAAM,UAAU;AAC/B,eAAW,CAAC,IAAI,KAAK,CAAC;AAAA,EACxB,WAAW,OAAO,KAAK,CAAC,MAAM,YAAY;AACxC,eAAW,CAAC,IAAI,KAAK,CAAC;AAAA,EACxB;AAEA,MAAI,OAAO,KAAK,CAAC,MAAM,YAAY;AACjC,eAAW,CAAC,IAAI,KAAK,CAAC;AAAA,EACxB;AAEA,SAAO;AACT;;;ADfO,IAAM,aAAN,cAAyB,IAAI,OAAO;AAAA,EAGzC,YAA+B,SAA4B;AACzD,UAAM;AADuB;AAE7B,SAAK,aAAa;AAClB,SAAK,QAAQ;AAEb,SAAK,SAAS,CAAC,aAAa;AAC1B,eAAS,IAAI;AAAA,IACf;AAAA,EACF;AAAA,EAEO,UAAU;AAGf,SAAK,aAAa;AAClB,WAAO;AAAA,EACT;AAAA,EAEO,SAAS,MAA+B;AAC7C,UAAM,CAAC,OAAO,UAAU,QAAQ,IAAI;AAAA,MAClC;AAAA,IACF;AACA,SAAK,QAAQ,MAAM,OAAO,UAAU,QAAQ;AAC5C,WAAO;AAAA,EACT;AAAA,EAEO,OAAO,MAAsB;AAClC,UAAM,CAAC,OAAO,UAAU,QAAQ,IAAI;AAAA,MAClC;AAAA,IACF;AACA,SAAK,QAAQ,MAAM,OAAO,UAAU,QAAQ;AAC5C,WAAO,MAAM,IAAI,MAAM,MAAM,IAAW;AAAA,EAC1C;AAAA,EAEO,KAAK,OAAY,UAAoC;AAC1D,SAAK,QAAQ,KAAK,OAAO,QAAQ;AACjC,WAAO,MAAM,KAAK,OAAO,QAAQ;AAAA,EACnC;AACF;;;AEzDO,SAAS,6BAA6B,SAAmB;AAC9D,MAAI,UAAU,SAAS;AACrB,WAAO,IAAI,IAAI,QAAQ,IAAI;AAAA,EAC7B;AAEA,QAAM,WAAW,QAAQ,SAAS,MAAM,WAAW;AACnD,QAAM,OAAO,QAAQ;AAErB,QAAM,MAAM,IAAI,IAAI,GAAG,aAAa,MAAM;AAE1C,MAAI,QAAQ,MAAM;AAChB,QAAI,OAAO,QAAQ,KAAK,SAAS;AAAA,EACnC;AAEA,MAAI,QAAQ,MAAM;AAChB,QAAI,WAAW,QAAQ;AAAA,EACzB;AAEA,MAAI,QAAQ,MAAM;AAChB,UAAM,CAAC,UAAU,QAAQ,IAAI,QAAQ,KAAK,MAAM,GAAG;AACnD,QAAI,WAAW;AACf,QAAI,WAAW;AAAA,EACjB;AAEA,SAAO;AACT;;;ACrBA,IAAM,cAAc,OAAO,aAAa;AACxC,IAAM,kBAAkB,OAAO,iBAAiB;AAEhD,SAAS,gBACP,SACA,MACA,UACA;AACA,yBAAuB,SAAS,CAAC,CAAC;AAClC,QAAM,aAAa,QAAQ,IAAI,SAAS,WAAW;AAEnD,MAAI,aAAa,OAAO;AAEtB,aAAS,QAAQ,WAAW,SAAS,GAAG,SAAS,GAAG,SAAS;AAC3D,UAAI,WAAW,KAAK,EAAE,CAAC,EAAE,YAAY,MAAM,KAAK,CAAC,EAAE,YAAY,GAAG;AAChE,mBAAW,OAAO,OAAO,CAAC;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AAEA,aAAW,KAAK,IAAI;AACtB;AAMA,SAAS,uBACP,SACA,YACM;AACN,MAAI,QAAQ,IAAI,SAAS,WAAW,GAAG;AACrC;AAAA,EACF;AAEA,yBAAuB,SAAS,UAAU;AAC5C;AAMA,SAAS,uBAAuB,SAAkB,YAAwB;AACxE,SAAO,eAAe,SAAS,aAAa;AAAA,IAC1C,OAAO;AAAA,IACP,YAAY;AAAA;AAAA;AAAA;AAAA,IAIZ,cAAc;AAAA,EAChB,CAAC;AACH;AAgBO,SAAS,wBAAwB;AAEtC,MAAI,QAAQ,IAAI,SAAS,eAAe,GAAG;AACzC,WAAO,QAAQ,IAAI,SAAS,eAAe;AAAA,EAC7C;AAEA,QAAM;AAAA,IACJ,SAAS;AAAA,IACT,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,IAAI;AACJ,QAAM,EAAE,KAAK,QAAQ,QAAQ,oBAAoB,IAAI,QAAQ;AAE7D,SAAO,eAAe,SAAS,iBAAiB;AAAA,IAC9C,OAAO,MAAM;AACX,cAAQ,UAAU,MAAM;AACxB,cAAQ,UAAU,SAAS;AAC3B,cAAQ,UAAU,SAAS;AAC3B,iBAAW,UAAU;AAErB,iBAAW,UAAU;AACrB,iBAAW,WAAW;AAEtB,cAAQ,eAAe,SAAS,eAAe;AAAA,IACjD;AAAA,IACA,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,IAKZ,cAAc;AAAA,EAChB,CAAC;AAED,SAAO,eAAe,YAAY,WAAW;AAAA,IAC3C,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,OAAO,IAAI,MAAM,SAAS;AAAA,MACxB,UAAU,QAAQ,MAAM,WAAW;AACjC,cAAM,cAAc,KAAK,CAAC,KAAK,CAAC;AAEhC,YACE,uBAAuB,WACvB,QAAQ,IAAI,aAAa,WAAW,GACpC;AACA,gBAAMC,WAAU,QAAQ;AAAA,YACtB;AAAA,YACA,CAAC,QAAQ,IAAI,aAAa,WAAW,CAAC;AAAA,YACtC;AAAA,UACF;AACA,iCAAuBA,UAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAM9B,GAAG,QAAQ,IAAI,aAAa,WAAW;AAAA,UACzC,CAAC;AACD,iBAAOA;AAAA,QACT;AAEA,cAAM,UAAU,QAAQ,UAAU,QAAQ,MAAM,SAAS;AAMzD,YAAI,CAAC,QAAQ,IAAI,SAAS,WAAW,GAAG;AACtC,gBAAM,iBAAiB,MAAM,QAAQ,WAAW,IAC5C,cACA,OAAO,QAAQ,WAAW;AAC9B,iCAAuB,SAAS,cAAc;AAAA,QAChD;AAEA,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AAED,UAAQ,UAAU,MAAM,IAAI,MAAM,QAAQ,UAAU,KAAK;AAAA,IACvD,MAAM,QAAQ,SAAS,MAAmB;AACxC,sBAAgB,SAAS,MAAM,KAAK;AACpC,aAAO,QAAQ,MAAM,QAAQ,SAAS,IAAI;AAAA,IAC5C;AAAA,EACF,CAAC;AAED,UAAQ,UAAU,SAAS,IAAI,MAAM,QAAQ,UAAU,QAAQ;AAAA,IAC7D,MAAM,QAAQ,SAAS,MAAmB;AACxC,sBAAgB,SAAS,MAAM,QAAQ;AACvC,aAAO,QAAQ,MAAM,QAAQ,SAAS,IAAI;AAAA,IAC5C;AAAA,EACF,CAAC;AAED,UAAQ,UAAU,SAAS,IAAI,MAAM,QAAQ,UAAU,QAAQ;AAAA,IAC7D,MAAM,QAAQ,SAAS,MAAgB;AACrC,YAAM,aAAa,QAAQ,IAAI,SAAS,WAAW;AAEnD,UAAI,YAAY;AACd,iBAAS,QAAQ,WAAW,SAAS,GAAG,SAAS,GAAG,SAAS;AAC3D,cAAI,WAAW,KAAK,EAAE,CAAC,EAAE,YAAY,MAAM,KAAK,CAAC,EAAE,YAAY,GAAG;AAChE,uBAAW,OAAO,OAAO,CAAC;AAAA,UAC5B;AAAA,QACF;AAAA,MACF;AAEA,aAAO,QAAQ,MAAM,QAAQ,SAAS,IAAI;AAAA,IAC5C;AAAA,EACF,CAAC;AAED,SAAO,eAAe,YAAY,WAAW;AAAA,IAC3C,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,OAAO,IAAI,MAAM,SAAS;AAAA,MACxB,UAAU,QAAQ,MAAM,WAAW;AACjC,cAAM,UAAU,QAAQ,UAAU,QAAQ,MAAM,SAAS;AACzD,cAAM,qBAAiC,CAAC;AAGxC,YAAI,OAAO,KAAK,CAAC,MAAM,YAAY,KAAK,CAAC,EAAE,WAAW,MAAM;AAC1D,6BAAmB,KAAK,GAAG,gBAAgB,KAAK,CAAC,EAAE,OAAO,CAAC;AAAA,QAC7D;AAGA,YAAI,OAAO,KAAK,CAAC,MAAM,YAAY,KAAK,CAAC,EAAE,WAAW,MAAM;AAC1D,6BAAmB,KAAK,GAAG,gBAAgB,KAAK,CAAC,EAAE,OAAO,CAAC;AAAA,QAC7D;AAEA,YAAI,mBAAmB,SAAS,GAAG;AACjC,iCAAuB,QAAQ,SAAS,kBAAkB;AAAA,QAC5D;AAEA,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AAED,SAAO,eAAe,YAAY,YAAY;AAAA,IAC5C,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,OAAO,IAAI,MAAM,UAAU;AAAA,MACzB,UAAU,QAAQ,MAAM,WAAW;AACjC,cAAM,WAAW,QAAQ,UAAU,QAAQ,MAAM,SAAS;AAE1D,YAAI,OAAO,KAAK,CAAC,MAAM,YAAY,KAAK,CAAC,EAAE,WAAW,MAAM;AAC1D;AAAA,YACE,SAAS;AAAA,YACT,gBAAgB,KAAK,CAAC,EAAE,OAAO;AAAA,UACjC;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAEO,SAAS,0BAA0B;AACxC,MAAI,CAAC,QAAQ,IAAI,SAAS,eAAe,GAAG;AAC1C;AAAA,EACF;AAEA,UAAQ,IAAI,SAAS,eAAe,EAAE;AACxC;AAEO,SAAS,mBAAmB,SAA8B;AAG/D,MAAI,CAAC,QAAQ,IAAI,SAAS,WAAW,GAAG;AACtC,WAAO,MAAM,KAAK,QAAQ,QAAQ,CAAC;AAAA,EACrC;AAEA,QAAM,aAAa,QAAQ,IAAI,SAAS,WAAW;AACnD,SAAO,WAAW,SAAS,IAAI,aAAa,MAAM,KAAK,QAAQ,QAAQ,CAAC;AAC1E;AAYA,SAAS,gBAAgB,SAAkC;AACzD,MAAI,mBAAmB,SAAS;AAC9B,WAAO,QAAQ,IAAI,SAAS,WAAW,KAAK,CAAC;AAAA,EAC/C;AAEA,SAAO,QAAQ,IAAI,IAAI,QAAQ,OAAO,GAAG,WAAW;AACtD;;;AJzNO,IAAM,aAAa,OAAO,YAAY;AAEtC,IAAM,iBAAN,cAA6B,WAAW;AAAA,EAoB7C,YAAY,SAAgC;AAC1C,UAAM;AAAA,MACJ,OAAO,CAAC,OAAO,UAAU,aAAa;AApE5C;AAwEQ,YAAI,KAAK,gBAAgB,eAAe;AACtC,eAAK,YAAY,KAAK,CAAC,OAAO,UAAU,QAAQ,CAAC;AAAA,QACnD;AAEA,YAAI,OAAO;AAMT,cAAI,KAAK,gBAAgB,eAAe;AACtC,uBAAK,mBAAL,mBAAqB,MAAM,OAAO,UAAU;AAAA,UAC9C;AAEA,eAAK,cAAc;AAAA,YACjB,OAAO,SAAS,KAAK,IAAI,QAAQ,OAAO,KAAK,OAAO,QAAQ;AAAA,UAC9D;AAAA,QACF;AAAA,MACF;AAAA,MACA,MAAM,CAAC,UAAU;AACf,YAAI,UAAU,MAAM;AAMlB,eAAK,eAAe;AAAA,YAClB,OAAO,SAAS,KAAK,IAAI,QAAQ,OAAO,KAAK,KAAK;AAAA,UACpD;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAhDH,SAAQ,cAAgD,CAAC;AAMzD,SAAQ,cAAkD;AA6Z1D,SAAQ,iBAAiD,CACvD,cACA,cACA,YACA,GACA,MACA,IACA,KACA,MACA,oBACG;AApeP;AAqeI,WAAK,kBAAkB;AAEvB,YAAM,MAAM,IAAI,IAAI,MAAM,KAAK,OAAO;AACtC,YAAM,WAAS,UAAK,kBAAkB,WAAvB,mBAA+B,kBAAiB;AAC/D,YAAM,UAAU,cAAc,gBAAgB,UAAU;AACxD,YAAM,cAAc,WAAW,SAAS,WAAW;AAInD,UAAI,IAAI,YAAY,IAAI,UAAU;AAChC,YAAI,CAAC,QAAQ,IAAI,eAAe,GAAG;AACjC,kBAAQ,IAAI,iBAAiB,SAAS,IAAI,YAAY,IAAI,UAAU;AAAA,QACtE;AACA,YAAI,WAAW;AACf,YAAI,WAAW;AAAA,MACjB;AAMA,WAAK,gBAAgB,IAAI,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMhC,MAAM,MAAM;AAKV,eAAK,iBAAiB;AAAA,QACxB;AAAA,MACF,CAAC;AAED,YAAM,YAAY,gBAAgB;AAClC,WAAK,UAAU,IAAI,QAAQ,KAAK;AAAA,QAC9B;AAAA,QACA;AAAA,QACA,aAAa;AAAA;AAAA,QAEb,QAAQ,cAAc,SAAS;AAAA,QAC/B,MAAM,cAAe,SAAS,MAAM,KAAK,aAAc,IAAY;AAAA,MACrE,CAAC;AAED,cAAQ,IAAI,KAAK,SAAS,YAAY,SAAS;AAI/C,oBAAc,KAAK,SAAS,QAAQ,IAAI,MAAM,cAAc,CAAC;AAK7D,8BAAwB,KAAK,SAAS,KAAK,aAAa;AAYxD,UAAI,KAAK,QAAQ,QAAQ,IAAI,+BAA+B,GAAG;AAC7D,aAAK,YAAY;AACjB;AAAA,MACF;AAEA,WAAK,UAAU;AAAA,QACb;AAAA,QACA,SAAS,KAAK;AAAA,QACd,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAkBA,SAAQ,kBAAmD,CACzD,cACA,cACA,YACA,QACA,KACA,QACA,eACG;AACH,YAAM,UAAU,cAAc,gBAAgB,UAAU;AAExD,YAAM,WAAW,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQnB,cAAc,mBAAmB,MAAM,IAClC,SAAS;AAAA,UACP,KAAK,iBAAiB,IAAI,SAAS,EAAE,OAAO;AAAA,UAAC,EAAE,CAAC;AAAA,QACnD,IACA;AAAA,QACJ;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA;AAAA,QACE,KAAK;AAAA,QACL;AAAA,MACF;AAEA,oBAAc,OAAO,KAAK,QAAQ,KAAK,QAAQ;AAO/C,UAAI,KAAK,QAAQ,QAAQ,IAAI,+BAA+B,GAAG;AAC7D;AAAA,MACF;AAEA,WAAK,2BAA2B,KAAK,WAAW;AAAA,QAC9C;AAAA,QACA,kBAAkB,KAAK,gBAAgB;AAAA,QACvC,WAAW,QAAQ,IAAI,KAAK,SAAS,UAAU;AAAA,QAC/C,SAAS,KAAK;AAAA,QACd,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAlhBE,SAAK,oBAAoB,QAAQ;AACjC,SAAK,mBAAmB,QAAQ;AAChC,SAAK,YAAY,QAAQ;AACzB,SAAK,aAAa,QAAQ;AAE1B,SAAK,UAAU,6BAA6B,KAAK,iBAAiB;AAGlE,SAAK,gBAAgB,IAAI,WAAW;AACpC,SAAK,cAAc,WAAW,WAAW,SAAS,CAAC,CAAC;AACpD,SAAK,cAAc,WAAW,kBAAkB,IAC9C,KAAK,eAAe,KAAK,IAAI;AAC/B,SAAK,cAAc,WAAW,OAAO,IAAI,KAAK,cAAc,KAAK,IAAI;AACrE,SAAK,cAAc,WAAW,kBAAkB,IAC9C,KAAK,aAAa,KAAK,IAAI;AAG7B,SAAK,iBAAiB,IAAI,WAAW;AACrC,SAAK,eAAe,WAAW,WAAW,UAAU,CAAC,CAAC;AACtD,SAAK,eAAe,WAAW,kBAAkB,IAC/C,KAAK,gBAAgB,KAAK,IAAI;AAChC,SAAK,eAAe,WAAW,OAAO,IAAI,KAAK,eAAe,KAAK,IAAI;AACvE,SAAK,eAAe,WAAW,kBAAkB,IAC/C,KAAK,cAAc,KAAK,IAAI;AAI9B,SAAK,KAAK,UAAU,MAAM,KAAK,cAAc,KAAK,CAAC;AAEnD,QAAI,KAAK,QAAQ,aAAa,UAAU;AACtC,cAAQ,IAAI,MAAM,aAAa,IAAI;AAGnC,cAAQ,IAAI,MAAM,cAAc,KAAK;AACrC,cAAQ,IAAI,MAAM,eAAe,MAAM,SAAS;AAChD,cAAQ,IAAI,MAAM,cAAc,MAAM,MAAS;AAC/C,cAAQ,IAAI,MAAM,mBAAmB,MAAM,KAAK;AAAA,IAClD;AAAA,EACF;AAAA,EAEO,KAAK,UAA2B,MAAsB;AAC3D,UAAM,YAAY,MAAM,KAAK,KAAK,MAAM,OAAc,GAAG,IAAI;AAE7D,QAAI,KAAK,0BAA0B;AACjC,WAAK,yBAAyB,QAAQ,SAAS;AAC/C,aAAO,KAAK,cAAc,KAAK,IAAI;AAAA,IACrC;AAEA,WAAO,UAAU;AAAA,EACnB;AAAA,EAEO,QAAQ,OAAiC;AAI9C,SAAK,eAAe,KAAK;AAEzB,QAAI,OAAO;AACT,WAAK,KAAK,SAAS,KAAK;AAAA,IAC1B;AAEA,WAAO,MAAM,QAAQ,KAAK;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMO,cAAoB;AACzB,SAAK,cAAc;AAEnB,QAAI,KAAK,WAAW;AAClB;AAAA,IACF;AAEA,UAAM,SAAS,KAAK,iBAAiB;AACrC,SAAK,iBAAiB;AAQtB,QAAI,aAAa,QAAQ;AACvB,aAAO,eAAe,MAAM,WAAW;AAAA,QACrC,OAAO,OAAO;AAAA,QACd,YAAY;AAAA,QACZ,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAGA,SAAK,KAAK,SAAS,CAAC,UAAU;AAC5B,aAAO,QAAQ,KAAK;AAAA,IACtB,CAAC;AAED,SAAK,UAAU,OAAO,QAAQ,KAAK,MAAM;AAMzC,QAAI;AACJ,QAAI,iBAAiB;AAErB,WAAQ,YAAY,KAAK,YAAY,MAAM,GAAI;AAC7C,UAAI,cAAc,QAAW;AAC3B,YAAI,CAAC,gBAAgB;AACnB,gBAAM,CAAC,OAAO,UAAU,QAAQ,IAAI;AACpC,gBAAM,cAAc,MAAM,SAAS;AACnC,gBAAM,4BAA4B,YAAY;AAAA,YAC5C;AAAA,YACA,YAAY,QAAQ,MAAM,IAAI;AAAA,UAChC;AACA,gBAAM,2BAA2B,YAAY;AAAA,YAC3C,MAAM,QAAQ,UAAU;AAAA,UAC1B;AACA,gBAAM,oBAAoB,mBAAmB,KAAK,QAAS,OAAO;AAClE,gBAAM,uBAAuB,kBAE1B,OAAO,CAAC,CAAC,IAAI,MAAM;AAClB,mBAAO,KAAK,YAAY,MAAM;AAAA,UAChC,CAAC,EACA,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,GAAG,SAAS,OAAO,EAC1C,KAAK,MAAM;AAKd,gBAAM,eAAe,GAAG,4BAA4B,uBAAuB;AAC3E,iBAAO,MAAM,cAAc,UAAU,QAAQ;AAC7C,2BAAiB;AACjB;AAAA,QACF;AAEA,eAAO,MAAM,GAAG,SAAS;AAAA,MAC3B;AAAA,IACF;AAIA,QAAI,QAAQ,IAAI,QAAQ,WAAW,GAAG;AACpC,YAAM,gBAAgB;AAAA,QACpB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,oBAAc,QAAQ,CAAC,iBAAiB;AACtC,eAAO,eAAe,MAAM,cAAc;AAAA,UACxC,YAAY;AAAA,UACZ,KAAK,MAAM;AACT,kBAAM,QAAQ,QAAQ,IAAI,QAAQ,YAAY;AAC9C,mBAAO,OAAO,UAAU,aAAa,MAAM,KAAK,MAAM,IAAI;AAAA,UAC5D;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAEA,WACG,GAAG,UAAU,IAAI,SAAS,KAAK,KAAK,UAAU,GAAG,IAAI,CAAC,EACtD,GAAG,WAAW,MAAM;AACnB,WAAK,aAAa,OAAO;AACzB,WAAK,KAAK,SAAS;AAAA,IACrB,CAAC,EACA,GAAG,iBAAiB,MAAM,KAAK,KAAK,eAAe,CAAC,EACpD,GAAG,UAAU,MAAM,KAAK,KAAK,QAAQ,CAAC,EACtC,GAAG,WAAW,CAAC,YAAY,KAAK,KAAK,WAAW,OAAO,CAAC,EACxD,GAAG,SAAS,MAAM,KAAK,KAAK,OAAO,CAAC,EACpC,GAAG,SAAS,MAAM,KAAK,KAAK,OAAO,CAAC,EACpC,GAAG,QAAQ,CAAC,UAAU;AAIrB,WAAK,KAAK,KAAK;AAAA,IACjB,CAAC,EACA,GAAG,SAAS,CAAC,UAAU;AACtB,cAAQ,IAAI,MAAM,aAAa,QAAQ,IAAI,QAAQ,WAAW,CAAC;AAC/D,WAAK,KAAK,SAAS,KAAK;AAAA,IAC1B,CAAC,EACA,GAAG,UAAU,MAAM,KAAK,KAAK,QAAQ,CAAC,EACtC,GAAG,WAAW,MAAM,KAAK,KAAK,SAAS,CAAC,EACxC,GAAG,aAAa,MAAM,KAAK,KAAK,WAAW,CAAC,EAC5C,GAAG,UAAU,MAAM,KAAK,KAAK,QAAQ,CAAC,EACtC,GAAG,SAAS,CAAC,aAAa,KAAK,KAAK,SAAS,QAAQ,CAAC,EACtD,GAAG,OAAO,MAAM,KAAK,KAAK,KAAK,CAAC;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAa,YAAY,UAAmC;AA5S9D;AA+SI,QAAI,KAAK,WAAW;AAClB;AAAA,IACF;AAGA,QAAI,qBAAqB,UAAU,MAAM,KAAK,SAAS,SAAS,SAAS;AACvE,WAAK,UAAU,IAAI,UAAU,eAAe,CAAC;AAC7C;AAAA,IACF;AAIA,SAAK,YAAY;AACjB,SAAK,cAAc;AAInB,SAAK,iBAAiB;AAItB,UAAM,iBAAiB,IAAI,eAAe,IAAI,gBAAgB,IAAI,CAAC;AAUnE,mBAAe;AAAA,MACb,IAAI,WAAW;AAAA,QACb,OAAO,CAAC,OAAO,UAAU,aAAa;AACpC,eAAK,KAAK,OAAO,QAAQ;AACzB;AAAA,QACF;AAAA,QACA,OAAO;AAAA,QAAC;AAAA,MACV,CAAC;AAAA,IACH;AAWA,mBAAe,aAAa,YAAY;AACxC,mBAAe,aAAa,MAAM;AAElC,UAAM,qBAAqB,mBAAmB,SAAS,OAAO;AAO9D,mBAAe;AAAA,MACb,SAAS;AAAA,MACT,SAAS,cAAc,aAAa,SAAS,MAAM;AAAA,MACnD;AAAA,IACF;AAGA,SAAK,KAAK,SAAS,MAAM;AACvB,qBAAe,QAAQ;AAAA,IACzB,CAAC;AAED,QAAI,SAAS,MAAM;AACjB,UAAI;AACF,cAAM,SAAS,SAAS,KAAK,UAAU;AAEvC,eAAO,MAAM;AACX,gBAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAE1C,cAAI,MAAM;AACR,2BAAe,IAAI;AACnB;AAAA,UACF;AAEA,yBAAe,MAAM,KAAK;AAAA,QAC5B;AAAA,MACF,SAAS,OAAP;AAEA,aAAK,YAAY,0BAA0B,KAAK,CAAC;AACjD;AAAA,MACF;AAAA,IACF,OAAO;AACL,qBAAe,IAAI;AAAA,IACrB;AAGA,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,KAAK,UAAU;AASpB,iBAAK,mBAAL,mBAAqB,KAAK;AAC1B,WAAK,KAAK,IAAI;AAAA,IAChB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKO,UAAU,OAAqB;AACpC,SAAK,QAAQ,KAAK;AAAA,EACpB;AAAA,EAEQ,cAAoB;AAG1B,SAAK,aAAa;AAElB,UAAM,SACJC,KAAI,OAAO,KAAK,kBAAkB,QAAQ,KAC1C,KAAK,kBAAkB,WAAW;AACpC,UAAM,cAAc;AAAA,MAClB,SAAS,SAAS,QAAQ;AAAA,MAC1B,QAAQ,SAAS,SAAS;AAAA,MAC1B,MAAM,KAAK,kBAAkB;AAAA,IAC/B;AAEA,SAAK,UAAU,MAAM;AACrB,SAAK;AAAA,MACH;AAAA,MACA;AAAA,MACA,YAAY;AAAA,MACZ,YAAY,WAAW,SAAS,IAAI;AAAA,MACpC,KAAK,kBAAkB;AAAA,IACzB;AACA,SAAK,KAAK,SAAS;AACnB,SAAK,KAAK,OAAO;AAEjB,QAAI,KAAK,QAAQ,aAAa,UAAU;AACtC,WAAK,KAAK,QAAQ;AAClB,WAAK,KAAK,eAAe;AAGzB,WAAK;AAAA,QACH;AAAA,QACA,KAAK,kBAAkB,WACrB,OAAO,KAAK,0BAA0B;AAAA,MAC1C;AACA,WAAK,KAAK,WAAW,OAAO,KAAK,qBAAqB,CAAC;AAAA,IACzD;AAAA,EACF;AAAA,EAEQ,mBAAyB;AAC/B,eAAW,aAAa,KAAK,aAAa;AACxC,UAAI,OAAO,UAAU,CAAC,MAAM,YAAY;AACtC,kBAAU,CAAC,EAAE;AAOb,kBAAU,CAAC,IAAI;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AAAA,EA4FQ,cAAc,OAAqB;AACzC;AAAA,MACE,KAAK;AAAA,MACL;AAAA,IACF;AAEA,SAAK,cAAc,KAAK,KAAK;AAAA,EAC/B;AAAA,EAEQ,eAAqB;AAE3B,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,KAAK,IAAI;AAAA,IAC9B;AAAA,EACF;AAAA,EA2DQ,eAAe,OAAe;AACpC;AAAA,MACE,KAAK;AAAA,MACL;AAAA,IACF;AAEA,SAAK,eAAe,KAAK,KAAK;AAAA,EAChC;AAAA,EAEQ,gBAAsB;AAE5B,QAAI,KAAK,gBAAgB;AACvB,WAAK,eAAe,KAAK,IAAI;AAAA,IAC/B;AAAA,EACF;AACF;;;AK3oBA,OAAO,UAAU;AACjB,OAAO,WAAW;AAoBX,IAAM,YAAN,cAAwB,KAAK,MAAM;AAAA,EAKxC,YAAY,SAA2B;AACrC,UAAM;AACN,SAAK,cAAc,QAAQ;AAC3B,SAAK,YAAY,QAAQ;AACzB,SAAK,aAAa,QAAQ;AAAA,EAC5B;AAAA,EAEO,iBAAiB,SAAc,UAA2B;AAC/D,UAAM,mBACJ,KAAK,uBAAuB,KAAK,QAC7B,KAAK,YAAY,mBACjB,MAAM;AAEZ,UAAM,0BACJ,KAAK,uBAAuB,KAAK,QAC7B;AAAA,MACE,GAAG;AAAA,MACH,GAAG,KAAK,YAAY;AAAA,IACtB,IACA;AAEN,UAAM,SAAS,IAAI,eAAe;AAAA,MAChC,mBAAmB;AAAA,MACnB,kBAAkB,iBAAiB;AAAA,QACjC,KAAK,eAAe;AAAA,QACpB;AAAA,QACA;AAAA,MACF;AAAA,MACA,WAAW,KAAK,UAAU,KAAK,IAAI;AAAA,MACnC,YAAY,KAAK,WAAW,KAAK,IAAI;AAAA,IACvC,CAAC;AAED,WAAO;AAAA,EACT;AACF;AAEO,IAAM,iBAAN,cAA6B,MAAM,MAAM;AAAA,EAK9C,YAAY,SAA2B;AACrC,UAAM;AACN,SAAK,cAAc,QAAQ;AAC3B,SAAK,YAAY,QAAQ;AACzB,SAAK,aAAa,QAAQ;AAAA,EAC5B;AAAA,EAEO,iBAAiB,SAAc,UAA2B;AAC/D,UAAM,mBACJ,KAAK,uBAAuB,MAAM,QAC9B,KAAK,YAAY,mBACjB,MAAM;AAEZ,UAAM,0BACJ,KAAK,uBAAuB,MAAM,QAC9B;AAAA,MACE,GAAG;AAAA,MACH,GAAG,KAAK,YAAY;AAAA,IACtB,IACA;AAEN,UAAM,SAAS,IAAI,eAAe;AAAA,MAChC,mBAAmB;AAAA,MACnB,kBAAkB,iBAAiB;AAAA,QACjC,KAAK,eAAe;AAAA,QACpB;AAAA,QACA;AAAA,MACF;AAAA,MACA,WAAW,KAAK,UAAU,KAAK,IAAI;AAAA,MACnC,YAAY,KAAK,WAAW,KAAK,IAAI;AAAA,IACvC,CAAC;AAED,WAAO;AAAA,EACT;AACF;;;ACtGA,SAAS,wBAAwB;AACjC;AAAA,EACE,SAAS;AAAA,EACT,eAAe;AAAA,OAEV;AACP;AAAA,EAEE,SAAS;AAAA,EACT,eAAe;AAAA,OACV;AACP;AAAA,EAOE,OAAAC;AAAA,EAEA,SAAS;AAAA,OACJ;AACP,SAAS,UAAAC,eAAc;;;ACtBvB,SAAS,aAAa;AAEtB,SAAS,cAAc;AAEvB,IAAM,SAAS,IAAI,OAAO,8BAA8B;AAWjD,IAAM,eAAe;AAC5B,IAAM,mBAAmB;AACzB,IAAM,mBAAmB;AACzB,IAAM,WAAW;AAEjB,SAAS,SACP,SACgC;AAChC,SAAO,QAAQ,iBAAiB,QAAQ,QAAQ,QAAQ;AAC1D;AAEA,SAAS,4BAA4B,SAAyC;AA1B9E;AA2BE,MAAI,QAAQ,UAAU;AACpB,WAAO,QAAQ;AAAA,EACjB;AAEA,QAAM,QAAQ,SAAS,OAAO;AAC9B,QAAM,gBAAiB,+BAA0B;AAEjD,MAAI,eAAe;AACjB,WAAO;AAAA,EACT;AAEA,QAAM,OAAO,wBAAwB,OAAO;AAC5C,QAAM,kBAAkB,QAAQ,QAAQ,SAAS;AAEjD,SAAO,kBAAkB,aAAW,aAAQ,QAAR,mBAAa,aAAY;AAC/D;AAEA,SAAS,wBACP,SACoB;AAEpB,MAAI,QAAQ,MAAM;AAChB,WAAO,OAAO,QAAQ,IAAI;AAAA,EAC5B;AAGA,QAAM,QAAQ,SAAS,OAAO;AAE9B,MAAK,+BAAsB,QAAQ,MAAM;AACvC,WAAO,OAAQ,MAAqB,QAAQ,IAAI;AAAA,EAClD;AAEA,MAAK,+BAA0B,aAAa;AAC1C,WAAO,OAAQ,MAAyB,WAAW;AAAA,EACrD;AAIA,SAAO;AACT;AAOA,SAAS,wBACP,SACyB;AACzB,MAAI,QAAQ,MAAM;AAChB,UAAM,CAAC,UAAU,QAAQ,IAAI,QAAQ,KAAK,MAAM,GAAG;AACnD,WAAO,EAAE,UAAU,SAAS;AAAA,EAC9B;AACF;AAOA,SAAS,iBAAiB,MAAuB;AAC/C,SAAO,KAAK,SAAS,GAAG,KAAK,CAAC,KAAK,WAAW,GAAG,KAAK,CAAC,KAAK,SAAS,GAAG;AAC1E;AAEA,SAAS,YAAY,SAAqD;AACxE,MAAI,OAAO,QAAQ,YAAY,QAAQ;AAEvC,MAAI,MAAM;AACR,QAAI,iBAAiB,IAAI,GAAG;AACzB,aAAO,IAAI;AAAA,IACd;AAIA,WAAO,IAAI,IAAI,UAAU,MAAM,EAAE;AAAA,EACnC;AAEA,SAAO;AACT;AAKO,SAAS,uBAAuB,SAAsC;AAC3E,SAAO,KAAK,mBAAmB,OAAO;AAEtC,MAAI,QAAQ,KAAK;AACf,WAAO;AAAA,MACL;AAAA,MACA,QAAQ;AAAA,IACV;AACA,WAAO,IAAI,IAAI,QAAQ,IAAI,IAAI;AAAA,EACjC;AAEA,SAAO,KAAK,0CAA0C;AAEtD,QAAM,WAAW,4BAA4B,OAAO;AACpD,SAAO,KAAK,YAAY,QAAQ;AAEhC,QAAM,OAAO,wBAAwB,OAAO;AAC5C,SAAO,KAAK,QAAQ,IAAI;AAExB,QAAM,WAAW,YAAY,OAAO;AACpC,SAAO,KAAK,YAAY,QAAQ;AAEhC,QAAM,OAAO,QAAQ,QAAQ;AAC7B,SAAO,KAAK,QAAQ,IAAI;AAExB,QAAM,cAAc,wBAAwB,OAAO;AACnD,SAAO,KAAK,eAAe,WAAW;AAEtC,QAAM,aAAa,cACf,GAAG,YAAY,YAAY,YAAY,cACvC;AACJ,SAAO,KAAK,gBAAgB,UAAU;AAEtC,QAAM,aAAa,OAAO,SAAS,cAAc,IAAI,SAAS;AAC9D,QAAM,MAAM,IAAI,IAAI,GAAG,aAAa,WAAW,aAAa,MAAM;AAClE,MAAI,YAAW,2CAAa,aAAY;AACxC,MAAI,YAAW,2CAAa,aAAY;AAExC,SAAO,KAAK,gBAAgB,GAAG;AAE/B,SAAO;AACT;;;ACvJA,SAAS,UAAAC,eAAc;AAEvB,IAAMC,UAAS,IAAID,QAAO,aAAa;AAEvC,SAAS,cAAc,KAAoC;AAJ3D;AAKE,EAAAC,QAAO,KAAK,oBAAoB,GAAG;AAEnC,MAAI,OAAO,QAAQ,GAAC,SAAI,gBAAJ,mBAAiB,OAAM;AACzC,IAAAA,QAAO,KAAK,kDAAkD;AAC9D,WAAO;AAAA,EACT;AAEA,EAAAA,QAAO,KAAK,oCAAoC,IAAI,YAAY,IAAI;AACpE,SAAO,IAAI,YAAY,SAAS;AAClC;AAEO,SAAS,YACd,KACY;AACZ,EAAAA,QAAO,KAAK,mBAAmB,GAAG;AAElC,QAAM,uBAAuB,OAAO,QAAQ,GAAG,EAAE;AAAA,IAC/C,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM;AACrB,MAAAA,QAAO,KAAK,6BAA6B,KAAK,KAAK;AAGnD,UAAI,GAAG,IAAI,cAAc,KAAK,IAAI,YAAY,KAAK,IAAI;AACvD,aAAO;AAAA,IACT;AAAA,IACA,CAAC;AAAA,EACH;AAEA,SAAO,cAAc,GAAG,IACpB,uBACA,OAAO,OAAO,OAAO,eAAe,GAAG,GAAG,oBAAoB;AACpE;;;AFLA,IAAMC,UAAS,IAAIC,QAAO,iCAAiC;AAW3D,SAAS,sBACP,MACA,KACgB;AAGhB,MAAI,OAAO,KAAK,CAAC,MAAM,eAAe,OAAO,KAAK,CAAC,MAAM,YAAY;AACnE,IAAAD,QAAO,KAAK,uDAAuD,GAAG;AACtE,WAAO,iBAAiB,GAAG;AAAA,EAC7B;AAEA,MAAI,KAAK,CAAC,GAAG;AACX,IAAAA,QAAO,KAAK,8BAA8B,KAAK,CAAC,CAAC;AACjD,UAAM,wBAAwB,iBAAiB,GAAG;AAElD,IAAAA,QAAO,KAAK,wCAAwC,qBAAqB;AAOzE,IAAAA,QAAO,KAAK,2BAA2B;AACvC,UAAM,uBAAuB,YAAY,KAAK,CAAC,CAAC;AAChD,IAAAA,QAAO,KAAK,uCAAuC,oBAAoB;AAEvE,WAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AAEA,EAAAA,QAAO,KAAK,0CAA0C;AACtD,SAAO,CAAC;AACV;AAOA,SAAS,4BAA4B,KAAU,SAA8B;AAC3E,MAAI,OAAO,QAAQ,QAAQ,IAAI;AAC/B,MAAI,WAAW,QAAQ,YAAY,IAAI;AACvC,MAAI,OAAO,QAAQ,OAAO,QAAQ,KAAK,SAAS,IAAI,IAAI;AAExD,MAAI,QAAQ,MAAM;AAChB,UAAM,oBAAoB,SAAS,QAAQ,MAAM,KAAK;AACtD,QAAI,WAAW,kBAAkB,YAAY;AAC7C,QAAI,SAAS,kBAAkB,UAAU;AAAA,EAC3C;AAEA,SAAO;AACT;AAEA,SAAS,gBACP,MACiC;AACjC,SAAO,OAAO,KAAK,CAAC,MAAM,aAAa,KAAK,CAAC,IAAI,KAAK,CAAC;AACzD;AAYO,SAAS,2BACd,iBACA,MAC6B;AAC7B,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,EAAAA,QAAO,KAAK,aAAa,IAAI;AAC7B,EAAAA,QAAO,KAAK,2BAA2B,eAAe;AAItD,MAAI,KAAK,WAAW,GAAG;AACrB,UAAME,OAAM,IAAIC,KAAI,kBAAkB;AACtC,UAAMC,WAAU,sBAAsB,MAAMF,IAAG;AAC/C,WAAO,CAACA,MAAKE,QAAO;AAAA,EACtB;AAIA,MAAI,OAAO,KAAK,CAAC,MAAM,UAAU;AAC/B,IAAAJ,QAAO,KAAK,wCAAwC,KAAK,CAAC,CAAC;AAE3D,UAAM,IAAIG,KAAI,KAAK,CAAC,CAAC;AACrB,IAAAH,QAAO,KAAK,kBAAkB,GAAG;AAEjC,UAAM,wBAAwB,iBAAiB,GAAG;AAClD,IAAAA,QAAO,KAAK,6BAA6B,qBAAqB;AAE9D,cAAU,sBAAsB,MAAM,GAAG;AACzC,IAAAA,QAAO,KAAK,6BAA6B,OAAO;AAEhD,eAAW,gBAAgB,IAAI;AAAA,EACjC,WAGS,KAAK,CAAC,aAAaG,MAAK;AAC/B,UAAM,KAAK,CAAC;AACZ,IAAAH,QAAO,KAAK,4BAA4B,GAAG;AAO3C,QAAI,OAAO,KAAK,CAAC,MAAM,eAAe,SAAyB,KAAK,CAAC,CAAC,GAAG;AACvE,YAAM,4BAA4B,KAAK,KAAK,CAAC,CAAC;AAAA,IAChD;AAEA,cAAU,sBAAsB,MAAM,GAAG;AACzC,IAAAA,QAAO,KAAK,4BAA4B,OAAO;AAE/C,eAAW,gBAAgB,IAAI;AAAA,EACjC,WAGS,UAAU,KAAK,CAAC,KAAK,EAAE,YAAY,KAAK,CAAC,IAAI;AACpD,UAAM,CAAC,SAAS,IAAI;AACpB,IAAAA,QAAO,KAAK,mCAAmC,SAAS;AAExD,QAAI,UAAU,aAAa,MAAM;AAQ/B,MAAAA,QAAO,KAAK,4CAA4C;AAExD,aAAO,SAAS,KAAK,CAAC,CAAC,IACnB,2BAA2B,iBAAiB;AAAA,QAC1C,EAAE,MAAM,UAAU,MAAM,GAAG,KAAK,CAAC,EAAE;AAAA,QACnC,KAAK,CAAC;AAAA,MACR,CAAC,IACD,2BAA2B,iBAAiB;AAAA,QAC1C,EAAE,MAAM,UAAU,KAAK;AAAA,QACvB,KAAK,CAAC;AAAA,MACR,CAAC;AAAA,IACP;AAEA,IAAAA,QAAO,KAAK,8BAA8B;AAG1C,UAAM,cAAc,IAAIG,KAAI,UAAU,IAAI;AAE1C,WAAO,KAAK,CAAC,MAAM,SACf,2BAA2B,iBAAiB,CAAC,WAAW,CAAC,IACzD,OAAO,KAAK,CAAC,MAAM,aACnB,2BAA2B,iBAAiB,CAAC,aAAa,KAAK,CAAC,CAAC,CAAC,IAClE,2BAA2B,iBAAiB;AAAA,MAC1C;AAAA,MACA,KAAK,CAAC;AAAA,MACN,KAAK,CAAC;AAAA,IACR,CAAC;AAAA,EACP,WAGS,SAAS,KAAK,CAAC,CAAC,GAAG;AAC1B,cAAU,EAAE,GAAI,KAAK,CAAC,EAAU;AAChC,IAAAH,QAAO,KAAK,qCAAqC,OAAO;AAIxD,YAAQ,WAAW,QAAQ,YAAY;AACvC,IAAAA,QAAO,KAAK,+BAA+B,OAAO;AAElD,UAAM,uBAAuB,OAAO;AACpC,IAAAA,QAAO,KAAK,sCAAsC,IAAI,IAAI;AAE1D,eAAW,gBAAgB,IAAI;AAAA,EACjC,OAAO;AACL,UAAM,IAAI;AAAA,MACR,4DAA4D;AAAA,IAC9D;AAAA,EACF;AAEA,UAAQ,WAAW,QAAQ,YAAY,IAAI;AAC3C,UAAQ,SAAS,QAAQ,UAAU;AAUnC,MAAI,OAAO,QAAQ,UAAU,aAAa;AACxC,UAAM,QACJ,QAAQ,aAAa,WACjB,IAAI,WAAW;AAAA;AAAA,MAEb,GAAI,wBAAwB,WAAW;AAAA,QACrC,oBAAoB,QAAQ;AAAA,MAC9B;AAAA,IACF,CAAC,IACD,IAAI,UAAU;AAEpB,YAAQ,QAAQ;AAChB,IAAAA,QAAO,KAAK,4BAA4B,KAAK;AAAA,EAC/C;AAUA,MAAI,CAAC,QAAQ,eAAe;AAC1B,IAAAA,QAAO;AAAA,MACL;AAAA,MACA,QAAQ;AAAA,IACV;AAEA,YAAQ,gBACN,QAAQ,aAAa,WAAW,mBAAmB;AAAA,EACvD;AAEA,EAAAA,QAAO,KAAK,8BAA8B,IAAI,IAAI;AAClD,EAAAA,QAAO,KAAK,kCAAkC,OAAO;AACrD,EAAAA,QAAO,KAAK,mCAAmC,QAAQ;AASvD,MAAI,EAAE,eAAeG,OAAM;AACzB,UAAO,IAAY,SAAS;AAAA,EAC9B;AAEA,SAAO,CAAC,KAAK,SAAS,QAAQ;AAChC;;;AP/QO,IAAM,4BAAN,cAAuC,YAAiC;AAAA,EAG7E,cAAc;AACZ,UAAM,0BAAyB,MAAM;AAgIvC,SAAQ,YAA2C,OAAO;AAAA,MACxD;AAAA,MACA;AAAA,IACF,MAAM;AACJ,YAAM,YAAY,QAAQ,IAAI,SAAS,UAAU;AACjD,YAAM,aAAa,IAAI,kBAAkB,OAAO;AAEhD,YAAM,mBAAmB,MAAM,cAAc;AAAA,QAC3C;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS,KAAK;AAAA,QACd,YAAY,CAAC,aAAa;AACxB,iBAAO,YAAY,QAAQ;AAAA,QAC7B;AAAA,QACA,gBAAgB,CAAC,aAAa;AAC5B,iBAAO,YAAY,QAAQ;AAAA,QAC7B;AAAA,QACA,SAAS,CAAC,UAAU;AAClB,cAAI,iBAAiB,OAAO;AAC1B,mBAAO,UAAU,KAAK;AAAA,UACxB;AAAA,QACF;AAAA,MACF,CAAC;AAED,UAAI,CAAC,kBAAkB;AACrB,eAAO,OAAO,YAAY;AAAA,MAC5B;AAAA,IACF;AAEA,SAAO,aAA6C,OAAO;AAAA,MACzD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,MAAM;AAGJ,aAAO,UAAU,KAAK,SAAS,YAAY;AAAA,QACzC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EA3KA;AAAA,EAEU,QAAc;AACtB,UAAM;AAAA,MACJ,eAAe;AAAA,MACf,KAAK;AAAA,MACL,SAAS;AAAA,IACX,IAAIE;AACJ,UAAM,EAAE,KAAK,kBAAkB,SAAS,qBAAqB,IAAIC;AAEjE,UAAM,YAAY,KAAK,UAAU,KAAK,IAAI;AAC1C,UAAM,aAAa,KAAK,WAAW,KAAK,IAAI;AAG5C,IAAAD,MAAK,gBAAgB,IAAI,MAAMA,MAAK,eAAe;AAAA,MACjD,WAAW,CAAC,QAAQ,SAA0C;AAC5D,cAAM,CAAC,KAAK,SAAS,QAAQ,IAAI;AAAA,UAC/B;AAAA,UACA;AAAA,QACF;AAGA,cAAME,SAAQ,QAAQ,aAAa,WAAW,iBAAiB;AAC/D,cAAM,YAAY,IAAIA,OAAM;AAAA,UAC1B,aAAa,QAAQ;AAAA,UACrB;AAAA,UACA;AAAA,QACF,CAAC;AACD,gBAAQ,QAAQ;AAEhB,eAAO,QAAQ,UAAU,QAAQ,CAAC,KAAK,SAAS,QAAQ,CAAC;AAAA,MAC3D;AAAA,IACF,CAAC;AAED,IAAAF,MAAK,UAAU,IAAI,MAAMA,MAAK,SAAS;AAAA,MACrC,OAAO,CAAC,QAAQ,SAAS,SAA0C;AACjE,cAAM,CAAC,KAAK,SAAS,QAAQ,IAAI;AAAA,UAC/B;AAAA,UACA;AAAA,QACF;AACA,cAAM,YAAY,IAAI,UAAU;AAAA,UAC9B,aAAa,QAAQ;AAAA,UACrB;AAAA,UACA;AAAA,QACF,CAAC;AACD,gBAAQ,QAAQ;AAEhB,eAAO,QAAQ,MAAM,QAAQ,SAAS,CAAC,KAAK,SAAS,QAAQ,CAAC;AAAA,MAChE;AAAA,IACF,CAAC;AAED,IAAAA,MAAK,MAAM,IAAI,MAAMA,MAAK,KAAK;AAAA,MAC7B,OAAO,CAAC,QAAQ,SAAS,SAAsC;AAC7D,cAAM,CAAC,KAAK,SAAS,QAAQ,IAAI;AAAA,UAC/B;AAAA,UACA;AAAA,QACF;AAEA,cAAM,YAAY,IAAI,UAAU;AAAA,UAC9B,aAAa,QAAQ;AAAA,UACrB;AAAA,UACA;AAAA,QACF,CAAC;AACD,gBAAQ,QAAQ;AAEhB,eAAO,QAAQ,MAAM,QAAQ,SAAS,CAAC,KAAK,SAAS,QAAQ,CAAC;AAAA,MAChE;AAAA,IACF,CAAC;AAMD,IAAAC,OAAM,UAAU,IAAI,MAAMA,OAAM,SAAS;AAAA,MACvC,OAAO,CAAC,QAAQ,SAAS,SAA2C;AAClE,cAAM,CAAC,KAAK,SAAS,QAAQ,IAAI;AAAA,UAC/B;AAAA,UACA;AAAA,QACF;AAEA,cAAM,YAAY,IAAI,eAAe;AAAA,UACnC,aAAa,QAAQ;AAAA,UACrB;AAAA,UACA;AAAA,QACF,CAAC;AACD,gBAAQ,QAAQ;AAEhB,eAAO,QAAQ,MAAM,QAAQ,SAAS,CAAC,KAAK,SAAS,QAAQ,CAAC;AAAA,MAChE;AAAA,IACF,CAAC;AAED,IAAAA,OAAM,MAAM,IAAI,MAAMA,OAAM,KAAK;AAAA,MAC/B,OAAO,CAAC,QAAQ,SAAS,SAAuC;AAC9D,cAAM,CAAC,KAAK,SAAS,QAAQ,IAAI;AAAA,UAC/B;AAAA,UACA;AAAA,QACF;AAEA,cAAM,YAAY,IAAI,eAAe;AAAA,UACnC,aAAa,QAAQ;AAAA,UACrB;AAAA,UACA;AAAA,QACF,CAAC;AACD,gBAAQ,QAAQ;AAEhB,eAAO,QAAQ,MAAM,QAAQ,SAAS,CAAC,KAAK,SAAS,QAAQ,CAAC;AAAA,MAChE;AAAA,IACF,CAAC;AAKD,0BAAsB;AAEtB,SAAK,cAAc,KAAK,MAAM;AAC5B,MAAAD,MAAK,gBAAgB;AAErB,MAAAA,MAAK,MAAM;AACX,MAAAA,MAAK,UAAU;AAEf,MAAAC,OAAM,MAAM;AACZ,MAAAA,OAAM,UAAU;AAEhB,8BAAwB;AAAA,IAC1B,CAAC;AAAA,EACH;AA+CF;AAjLO,IAAM,2BAAN;AAAM,yBACJ,SAAS,OAAO,4BAA4B;", "names": ["http", "https", "net", "headers", "net", "URL", "<PERSON><PERSON>", "<PERSON><PERSON>", "logger", "logger", "<PERSON><PERSON>", "url", "URL", "options", "http", "https", "Agent"]}