import 'graphql';
export { E as ExpectedOperationTypeNode, G as GraphQLHandler, y as GraphQLHandlerInfo, m as GraphQLHandlerNameSelector, h as GraphQLJsonRequestBody, e as GraphQLQuery, g as GraphQLRequestBody, z as GraphQLRequestParsedResult, n as GraphQLResolverExtras, o as GraphQLResponseBody, f as GraphQLVariables, B as isDocumentNode } from '../HttpResponse-I457nh8V.mjs';
import '../utils/matching/matchRequestUrl.mjs';
import '@mswjs/interceptors';
import '../utils/internal/isIterable.mjs';
import '../typeUtils.mjs';
