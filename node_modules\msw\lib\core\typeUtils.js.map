{"version": 3, "sources": ["../../src/core/typeUtils.ts"], "sourcesContent": ["type Fn = (...arg: any[]) => any\n\nexport type MaybePromise<T> = T | Promise<T>\n\nexport type RequiredDeep<\n  Type,\n  U extends Record<string, unknown> | Fn | undefined = undefined,\n> = Type extends Fn\n  ? Type\n  : /**\n     * @note The \"Fn\" type satisfies the predicate below.\n     * It must always come first, before the Record check.\n     */\n    Type extends Record<string, any>\n    ? {\n        [Key in keyof Type]-?: NonNullable<Type[Key]> extends NonNullable<U>\n          ? NonNullable<Type[Key]>\n          : RequiredDeep<NonNullable<Type[Key]>, U>\n      }\n    : Type\n\n/**\n * @fixme Remove this once TS 5.4 is the lowest supported version.\n * Because \"NoInfer\" is a built-in type utility there.\n */\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}