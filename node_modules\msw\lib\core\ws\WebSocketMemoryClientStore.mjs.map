{"version": 3, "sources": ["../../../src/core/ws/WebSocketMemoryClientStore.ts"], "sourcesContent": ["import { WebSocketClientConnectionProtocol } from '@mswjs/interceptors/lib/browser/interceptors/WebSocket'\nimport {\n  SerializedWebSocketClient,\n  WebSocketClientStore,\n} from './WebSocketClientStore'\n\nexport class WebSocketMemoryClientStore implements WebSocketClientStore {\n  private store: Map<string, SerializedWebSocketClient>\n\n  constructor() {\n    this.store = new Map()\n  }\n\n  public async add(client: WebSocketClientConnectionProtocol): Promise<void> {\n    this.store.set(client.id, { id: client.id, url: client.url.href })\n  }\n\n  public getAll(): Promise<Array<SerializedWebSocketClient>> {\n    return Promise.resolve(Array.from(this.store.values()))\n  }\n\n  public async deleteMany(clientIds: Array<string>): Promise<void> {\n    for (const clientId of clientIds) {\n      this.store.delete(clientId)\n    }\n  }\n}\n"], "mappings": "AAMO,MAAM,2BAA2D;AAAA,EAC9D;AAAA,EAER,cAAc;AACZ,SAAK,QAAQ,oBAAI,IAAI;AAAA,EACvB;AAAA,EAEA,MAAa,IAAI,QAA0D;AACzE,SAAK,MAAM,IAAI,OAAO,IAAI,EAAE,IAAI,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK,CAAC;AAAA,EACnE;AAAA,EAEO,SAAoD;AACzD,WAAO,QAAQ,QAAQ,MAAM,KAAK,KAAK,MAAM,OAAO,CAAC,CAAC;AAAA,EACxD;AAAA,EAEA,MAAa,WAAW,WAAyC;AAC/D,eAAW,YAAY,WAAW;AAChC,WAAK,MAAM,OAAO,QAAQ;AAAA,IAC5B;AAAA,EACF;AACF;", "names": []}