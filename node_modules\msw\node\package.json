{"type": "commonjs", "browser": null, "main": "../lib/node/index.js", "module": "../lib/node/index.mjs", "types": "../lib/node/index.d.ts", "exports": {".": {"module-sync": {"types": "./../lib/node/index.d.mts", "default": "./../lib/node/index.mjs"}, "module": {"types": "./../lib/node/index.d.mts", "default": "./../lib/node/index.mjs"}, "node": {"require": "./../lib/node/index.js", "import": "./../lib/node/index.mjs"}, "import": {"types": "./../lib/node/index.d.mts", "default": "./../lib/node/index.mjs"}, "browser": null, "react-native": null, "default": {"types": "./../lib/node/index.d.ts", "default": "./../lib/node/index.js"}}}}