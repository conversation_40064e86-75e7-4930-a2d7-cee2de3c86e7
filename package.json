{"name": "automate", "description": "Automate system for video and content creation using the OpenAI API", "version": "0.1.0", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "test": "vitest", "test:run": "vitest run", "test:watch": "vitest --watch", "migrate": "node-pg-migrate up", "migrate:down": "node-pg-migrate down", "migrate:create": "node-pg-migrate create", "dev": "node --watch index.js"}, "author": "<PERSON><PERSON>", "dependencies": {"dotenv": "^16.5.0", "node-pg-migrate": "^8.0.2", "openai": "^5.3.0", "pg": "^8.16.0", "winston": "^3.17.0"}, "devDependencies": {"msw": "^2.10.2", "vitest": "^3.2.3"}}