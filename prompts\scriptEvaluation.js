/**
 * Prompt para evaluar guiones de videos de YouTube
 * @param {object} script - G<PERSON>ón a evaluar
 */
export const evaluateScriptPrompt = (script) => {
  return `
Evalúa el siguiente guión de video de YouTube usando un sistema de puntuación profesional y detallado.

GUIÓN A EVALUAR:
Título: ${script.title}
Contenido: ${script.content}

CRITERIOS DE EVALUACIÓN (100 puntos totales):

1. HOOK INICIAL (0-20 puntos)
   Evalúa los primeros 15 segundos:
   - ¿Capta la atención inmediatamente? (0-5 pts)
   - ¿Es relevante al tema principal? (0-5 pts)
   - ¿Crea curiosidad o urgencia? (0-5 pts)
   - ¿Es memorable y quotable? (0-5 pts)

2. ESTRUCTURA Y ORGANIZACIÓN (0-20 puntos)
   Evalúa la organización general:
   - ¿Fluye lógicamente de punto a punto? (0-5 pts)
   - ¿Tiene transiciones claras? (0-5 pts)
   - ¿Mantiene un ritmo adecuado? (0-5 pts)
   - ¿La duración es apropiada para el contenido? (0-5 pts)

3. ENGAGEMENT Y RETENCIÓN (0-20 puntos)
   Evalúa el potencial de mantener audiencia:
   - ¿Incluye elementos sorpresa o revelaciones? (0-5 pts)
   - ¿Varía el ritmo para evitar monotonía? (0-5 pts)
   - ¿Incluye momentos de interacción con audiencia? (0-5 pts)
   - ¿Tiene puntos de alta energía distribuidos? (0-5 pts)

4. CALL-TO-ACTIONS Y CONVERSIÓN (0-20 puntos)
   Evalúa las llamadas a la acción:
   - ¿Incluye CTAs naturales y bien ubicados? (0-5 pts)
   - ¿Motiva suscripciones de forma efectiva? (0-5 pts)
   - ¿Fomenta comentarios e interacción? (0-5 pts)
   - ¿Incluye teaser para próximo contenido? (0-5 pts)

5. POTENCIAL VIRAL Y COMPARTIBLE (0-20 puntos)
   Evalúa el potencial de viralización:
   - ¿Tiene momentos altamente compartibles? (0-5 pts)
   - ¿Incluye elementos trending o actuales? (0-5 pts)
   - ¿Es relevante para audiencia amplia? (0-5 pts)
   - ¿Tiene valor educativo o entretenimiento alto? (0-5 pts)

CRITERIOS ADICIONALES A CONSIDERAR:
- Originalidad del enfoque
- Calidad del contenido educativo/entretenimiento
- Potencial de generar discusión
- Adaptabilidad a diferentes formatos (Shorts, etc.)
- Claridad del mensaje principal

FORMATO DE RESPUESTA REQUERIDO (JSON válido):
{
  "total_score": número del 0 al 100,
  "hook_score": número del 0 al 20,
  "structure_score": número del 0 al 20,
  "engagement_score": número del 0 al 20,
  "cta_score": número del 0 al 20,
  "viral_score": número del 0 al 20,
  "feedback": "Análisis detallado de fortalezas y debilidades específicas. Incluye sugerencias concretas de mejora. Mínimo 200 palabras con ejemplos específicos del guión.",
  "strengths": ["Lista de 3-5 fortalezas principales"],
  "weaknesses": ["Lista de 3-5 debilidades principales"],
  "improvement_suggestions": ["Lista de 3-5 sugerencias específicas de mejora"]
}

INSTRUCCIONES IMPORTANTES:
- Sé crítico pero constructivo en la evaluación
- Proporciona ejemplos específicos del guión en el feedback
- Las puntuaciones deben reflejar estándares profesionales de YouTube
- Un score de 70+ indica contenido de alta calidad
- Un score de 85+ indica potencial viral excepcional
- Incluye sugerencias actionables para mejorar
`;
};
