import { delay, http, HttpResponse } from 'msw';

// Respuestas mock para OpenAI
const mockResponses = {
  topics: {
    "topics": [
      {
        "title": "Los 10 Gadgets Más Innovadores de 2024",
        "description": "Revisión completa de los dispositivos tecnológicos más revolucionarios del año",
        "popularity_score": 92
      },
      {
        "title": "<PERSON><PERSON><PERSON> Millonarios",
        "description": "Secretos y hábitos que practican los empresarios más exitosos cada mañana",
        "popularity_score": 88
      },
      {
        "title": "Reacciones a TikToks Virales Más Locos",
        "description": "Compilación y análisis de los videos más impactantes que rompieron internet",
        "popularity_score": 95
      },
      {
        "title": "Experimento: 30 Días Sin Redes Sociales",
        "description": "Documentando los efectos físicos y mentales de desconectarse completamente",
        "popularity_score": 85
      },
      {
        "title": "Comida Callejera Extrema Alrededor del Mundo",
        "description": "Probando los platillos más exóticos y peligrosos de diferentes culturas",
        "popularity_score": 90
      },
      {
        "title": "Trucos de Productividad que Cambiarán tu Vida",
        "description": "Técnicas respaldadas por ciencia para maximizar tu eficiencia diaria",
        "popularity_score": 82
      },
      {
        "title": "Construyendo una Casa Inteligente con $500",
        "description": "Tutorial paso a paso para automatizar tu hogar con presupuesto limitado",
        "popularity_score": 87
      },
      {
        "title": "Entrevista con Ex-Empleado de Tesla",
        "description": "Revelaciones exclusivas sobre la cultura laboral y secretos de la empresa",
        "popularity_score": 93
      },
      {
        "title": "Desafío: Sobrevivir 24h en el Bosque",
        "description": "Aventura extrema usando solo herramientas básicas de supervivencia",
        "popularity_score": 89
      },
      {
        "title": "Inversiones Crypto para Principiantes 2024",
        "description": "Guía completa y actualizada para entrar al mundo de las criptomonedas",
        "popularity_score": 91
      }
    ]
  },
  
  selection: "3,8,9",
  
  scripts: {
    "Reacciones a TikToks Virales Más Locos": `[0:00-0:15] HOOK: "¿Están listos para ver las reacciones más épicas a los TikToks que literalmente rompieron internet? Porque lo que van a ver hoy los va a dejar sin palabras..."
[Nota de producción: Mostrar montaje rápido de clips más impactantes]

[0:15-0:45] INTRODUCCIÓN: "¡Qué tal, familia! Soy [nombre] y hoy vamos a reaccionar a los TikToks más virales y locos que han aparecido últimamente. Si te gusta este contenido, no olvides suscribirte y activar la campanita porque subo videos así cada semana."
[Nota de producción: Animación de suscripción]

[0:45-3:00] PRIMER BLOQUE - TIKTOKS ÉPICOS: "Empezamos con este TikTok que tiene más de 50 millones de views... ¡Y cuando vean por qué, van a entender!"
[Nota de producción: Reacción genuina, pausar para comentarios]

[3:00-6:00] SEGUNDO BLOQUE - FAILS ÉPICOS: "Ahora vamos con los fails más épicos... Este me dolió hasta a mí verlo"
[Nota de producción: Efectos de sonido, zoom en reacciones]

[6:00-9:00] TERCER BLOQUE - TALENTO INCREÍBLE: "Pero no todo son fails, miren este talento increíble que me dejó con la boca abierta"
[Nota de producción: Música épica de fondo]

[9:00-10:30] CLÍMAX - EL MÁS VIRAL: "Y ahora... el TikTok que literalmente quebró internet. Más de 100 millones de views y cuando lo vean van a entender por qué"
[Nota de producción: Build-up dramático]

[10:30-11:00] CIERRE: "¿Cuál fue su favorito? Déjenme saber en los comentarios, den like si quieren más videos así, y nos vemos en el próximo video donde vamos a reaccionar a los memes más virales del mes. ¡Hasta la próxima!"
[Nota de producción: Pantalla final con videos sugeridos]`,

    "Entrevista con Ex-Empleado de Tesla": `[0:00-0:15] HOOK: "Un ex-empleado de Tesla me reveló secretos que Elon Musk no quiere que sepas... Y lo que me contó me dejó sin palabras"
[Nota de producción: Imagen misteriosa de Tesla, música intrigante]

[0:15-0:45] INTRODUCCIÓN: "Hola familia, hoy tengo una entrevista exclusiva con alguien que trabajó directamente en Tesla por 3 años. Por razones obvias, mantendremos su identidad en secreto, pero las revelaciones que van a escuchar son increíbles."
[Nota de producción: Silueta del entrevistado, voz modificada]

[0:45-3:00] PRIMER BLOQUE - LA CULTURA LABORAL: "Cuéntanos, ¿cómo es realmente trabajar para Elon Musk?"
[Nota de producción: Gráficos de Tesla, estadísticas]

[3:00-6:00] SEGUNDO BLOQUE - SECRETOS DE PRODUCCIÓN: "¿Hay cosas que el público no sabe sobre cómo se fabrican los Tesla?"
[Nota de producción: Footage de fábrica, diagramas técnicos]

[6:00-9:00] TERCER BLOQUE - PROYECTOS SECRETOS: "¿En qué proyectos secretos está trabajando Tesla que aún no conocemos?"
[Nota de producción: Conceptos futuristas, música épica]

[9:00-10:30] CLÍMAX - LA REVELACIÓN MÁS GRANDE: "Y ahora la bomba... ¿Cuál es el secreto más grande que puedes revelar?"
[Nota de producción: Pausa dramática, efectos visuales]

[10:30-11:00] CIERRE: "Increíble, ¿verdad? ¿Qué opinan de estas revelaciones? Comenten abajo, den like si quieren más entrevistas exclusivas así, y suscríbanse porque tengo más contenido explosivo coming soon. ¡Nos vemos!"
[Nota de producción: Call-to-action animado]`,

    "Desafío: Sobrevivir 24h en el Bosque": `[0:00-0:15] HOOK: "Me quedé 24 horas solo en el bosque con solo una navaja... Lo que pasó me cambió la vida para siempre"
[Nota de producción: Montaje dramático del bosque de noche]

[0:15-0:45] INTRODUCCIÓN: "¿Qué tal, aventureros? Hoy me embarco en el desafío más extremo de mi vida: sobrevivir 24 horas completas en el bosque usando solo herramientas básicas. Si logro llegar al final, ustedes deciden cuál será mi próximo desafío."
[Nota de producción: Mostrar equipo básico]

[0:45-3:00] PRIMER BLOQUE - PREPARACIÓN: "Primero necesito encontrar refugio antes de que oscurezca..."
[Nota de producción: Time-lapse de construcción de refugio]

[3:00-6:00] SEGUNDO BLOQUE - BÚSQUEDA DE COMIDA: "Ahora viene lo difícil... encontrar algo que comer"
[Nota de producción: Búsqueda de bayas, pesca improvisada]

[6:00-9:00] TERCER BLOQUE - LA NOCHE: "Llegó la noche y aquí es donde todo se pone real... Los sonidos del bosque dan miedo"
[Nota de producción: Audio nocturno, cámara infrarroja]

[9:00-10:30] CLÍMAX - LA MADRUGADA: "Son las 3 AM y algo se está moviendo cerca de mi refugio... No sé si podré completar el desafío"
[Nota de producción: Tensión máxima, sonidos misteriosos]

[10:30-11:00] CIERRE: "¡Lo logré! 24 horas completas. Esta experiencia me enseñó tanto sobre mí mismo. ¿Qué desafío quieren que haga próximo? Voten en los comentarios, den like si disfrutaron esta aventura, y suscríbanse para más contenido extremo. ¡Hasta la próxima aventura!"
[Nota de producción: Amanecer épico, música inspiracional]`
  },
  
  evaluations: {
    "Reacciones a TikToks Virales Más Locos": {
      "total_score": 87,
      "hook_score": 18,
      "structure_score": 17,
      "engagement_score": 19,
      "cta_score": 16,
      "viral_score": 17,
      "feedback": "El guión tiene un hook inicial muy atractivo que capta la atención inmediatamente con una promesa clara de contenido épico. La estructura está bien organizada con bloques temáticos claros que mantienen el interés. El engagement es alto debido a la naturaleza reactiva del contenido y las pausas para comentarios. Los CTAs están bien distribuidos pero podrían ser más creativos. El potencial viral es excelente debido al formato de reacciones que es muy compartible en redes sociales.",
      "strengths": ["Hook muy efectivo", "Estructura clara por bloques", "Alto potencial de engagement", "Formato muy compartible", "Buena distribución de CTAs"],
      "weaknesses": ["CTAs podrían ser más creativos", "Falta más interacción directa con audiencia", "Podría incluir más elementos sorpresa"],
      "improvement_suggestions": ["Agregar polls o preguntas directas", "Incluir CTAs más creativos y únicos", "Añadir elementos de gamificación"]
    },
    "Entrevista con Ex-Empleado de Tesla": {
      "total_score": 92,
      "hook_score": 19,
      "structure_score": 18,
      "engagement_score": 19,
      "cta_score": 17,
      "viral_score": 19,
      "feedback": "Excelente guión con un hook extremadamente poderoso que promete revelaciones exclusivas. La estructura de entrevista está perfectamente organizada con escalada de tensión. El engagement es excepcional debido al misterio y las revelaciones progresivas. Los CTAs están bien integrados sin interrumpir el flujo. El potencial viral es altísimo debido al factor de exclusividad y controversia controlada.",
      "strengths": ["Hook extremadamente poderoso", "Escalada perfecta de tensión", "Contenido exclusivo muy atractivo", "Excelente uso del misterio", "Alto valor de entretenimiento"],
      "weaknesses": ["Podría incluir más verificación de claims", "Riesgo de expectativas muy altas"],
      "improvement_suggestions": ["Incluir disclaimers apropiados", "Agregar elementos visuales de apoyo", "Considerar follow-up content"]
    },
    "Desafío: Sobrevivir 24h en el Bosque": {
      "total_score": 89,
      "hook_score": 18,
      "structure_score": 18,
      "engagement_score": 18,
      "cta_score": 17,
      "viral_score": 18,
      "feedback": "Guión muy sólido con un hook que promete transformación personal y aventura extrema. La estructura cronológica funciona perfectamente para este tipo de contenido. El engagement se mantiene alto con la progresión natural del desafío y momentos de tensión bien distribuidos. Los CTAs están integrados naturalmente. El potencial viral es alto debido al formato de desafío y la autenticidad de la experiencia.",
      "strengths": ["Progresión natural muy atractiva", "Momentos de tensión bien distribuidos", "Autenticidad del desafío", "Buen uso del time-lapse", "Mensaje inspiracional fuerte"],
      "weaknesses": ["Podría incluir más tips educativos", "Falta más interacción en tiempo real"],
      "improvement_suggestions": ["Agregar consejos de supervivencia", "Incluir Q&A en vivo", "Mostrar más del proceso de aprendizaje"]
    }
  }
};

// Función para determinar el tipo de respuesta basado en el prompt
function getResponseForPrompt(prompt) {
  if (prompt.includes('temas populares') || prompt.includes('Genera una lista')) {
    return JSON.stringify(mockResponses.topics);
  } else if (prompt.includes('selecciona los') || prompt.includes('mejores basándote')) {
    return mockResponses.selection;
  } else if (prompt.includes('Crea un guión') || prompt.includes('guión detallado')) {
    const topicMatch = prompt.match(/tema: "([^"]+)"/);
    const topicTitle = topicMatch ? topicMatch[1] : 'Tema Genérico';
    return mockResponses.scripts[topicTitle] || generateGenericScript(topicTitle);
  } else if (prompt.includes('Evalúa el siguiente guión') || prompt.includes('evalúa el siguiente')) {
    const titleMatch = prompt.match(/Título: ([^\n]+)/);
    const scriptTitle = titleMatch ? titleMatch[1].replace('Guión: ', '') : 'Script Genérico';
    return JSON.stringify(mockResponses.evaluations[scriptTitle] || generateGenericEvaluation());
  }
  
  return "Respuesta mock genérica de MSW";
}

function generateGenericScript(title) {
  return `[0:00-0:15] HOOK: "¡Prepárense para descubrir todo sobre ${title}!"
[0:15-0:45] INTRODUCCIÓN: "Hola familia, hoy vamos a hablar de ${title}..."
[0:45-8:00] DESARROLLO: "Aquí están los puntos principales sobre ${title}..."
[8:00-9:00] CLÍMAX: "Y ahora la parte más importante..."
[9:00-10:00] CIERRE: "¿Qué opinan sobre ${title}? Comenten abajo..."`;
}

function generateGenericEvaluation() {
  return {
    "total_score": Math.floor(Math.random() * 30) + 70,
    "hook_score": Math.floor(Math.random() * 5) + 15,
    "structure_score": Math.floor(Math.random() * 5) + 15,
    "engagement_score": Math.floor(Math.random() * 5) + 15,
    "cta_score": Math.floor(Math.random() * 5) + 15,
    "viral_score": Math.floor(Math.random() * 5) + 15,
    "feedback": "Evaluación generada automáticamente para pruebas. El guión muestra elementos sólidos en la mayoría de criterios evaluados.",
    "strengths": ["Estructura clara", "Contenido relevante", "Buen potencial"],
    "weaknesses": ["Podría mejorar engagement", "CTAs más creativos"],
    "improvement_suggestions": ["Agregar más interacción", "Mejorar hook inicial"]
  };
}

export const handlers = [
  // Interceptar llamadas a OpenAI API
  http.post('https://api.openai.com/v1/chat/completions', async ({ request,  }) => {
    const body = await request.json();
    const prompt = body.messages[0].content;

    // Simular delay de API real
    await delay('real');

    const mockContent = getResponseForPrompt(prompt);
    
    return HttpResponse.json({
      id: 'chatcmpl-mock',
      object: 'chat.completion',
      created: Date.now(),
      model: body.model,
      choices: [{
        index: 0,
        message: {
          role: 'assistant',
          content: mockContent
        },
        finish_reason: 'stop'
      }],
      usage: {
        prompt_tokens: prompt.length / 4,
        completion_tokens: mockContent.length / 4,
        total_tokens: (prompt.length + mockContent.length) / 4
      }
    });
  })
];
