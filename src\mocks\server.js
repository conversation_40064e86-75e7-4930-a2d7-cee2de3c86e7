import { setupServer } from 'msw/node';
import { handlers } from './handlers.js';

// Configurar el servidor MSW con los handlers
export const server = setupServer(...handlers);

// Función para inicializar MSW
export function setupMSW() {
  // Iniciar el servidor
  server.listen({
    onUnhandledRequest: 'bypass', // Permitir requests no interceptados
  });
  
  console.log('🎭 MSW (Mock Service Worker) iniciado - Interceptando llamadas a OpenAI API');
}

// Función para limpiar MSW
export function teardownMSW() {
  server.close();
  console.log('🎭 MSW detenido');
}
