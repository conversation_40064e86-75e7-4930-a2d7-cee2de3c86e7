import pkg from 'pg';
import logger from './logger.js';

const { Pool } = pkg;

class DatabaseService {
  constructor() {
    this.pool = new Pool({
      connectionString: process.env.DATABASE_URL,
    });
  }

  /**
   * Ejecuta una consulta SQL
   * @param {string} query - Consulta SQL
   * @param {Array} params - Parámetros de la consulta
   * @returns {Promise<object>} - Resultado de la consulta
   */
  async query(query, params = []) {
    try {
      logger.debug('Ejecutando consulta SQL', { 
        query: query.substring(0, 100) + '...',
        paramsCount: params.length 
      });

      const result = await this.pool.query(query, params);
      
      logger.debug('Consulta ejecutada exitosamente', { 
        rowCount: result.rowCount 
      });

      return result;
    } catch (error) {
      logger.failure('Error ejecutando consulta SQL', { 
        error: error.message,
        query: query.substring(0, 100) + '...'
      });
      throw error;
    }
  }

  /**
   * Guarda un tema en la base de datos
   * @param {object} topic - Datos del tema
   * @returns {Promise<object>} - Tema guardado
   */
  async saveTopic(topic) {
    const result = await this.query(
      'INSERT INTO topics (title, description, popularity_score) VALUES ($1, $2, $3) RETURNING *',
      [topic.title, topic.description, topic.popularity_score]
    );
    
    logger.success('Tema guardado', { topicId: result.rows[0].id, title: topic.title });
    return result.rows[0];
  }

  /**
   * Marca temas como seleccionados
   * @param {Array} topicIds - IDs de los temas a marcar
   * @returns {Promise<void>}
   */
  async markTopicsAsSelected(topicIds) {
    for (const topicId of topicIds) {
      await this.query(
        'UPDATE topics SET is_selected = true WHERE id = $1',
        [topicId]
      );
    }
    
    logger.success('Temas marcados como seleccionados', { count: topicIds.length });
  }

  /**
   * Guarda un guión en la base de datos
   * @param {number} topicId - ID del tema
   * @param {string} title - Título del guión
   * @param {string} content - Contenido del guión
   * @returns {Promise<object>} - Guión guardado
   */
  async saveScript(topicId, title, content) {
    const result = await this.query(
      'INSERT INTO scripts (topic_id, title, content) VALUES ($1, $2, $3) RETURNING *',
      [topicId, title, content]
    );
    
    logger.success('Guión guardado', { scriptId: result.rows[0].id, title });
    return result.rows[0];
  }

  /**
   * Actualiza el score de un guión
   * @param {number} scriptId - ID del guión
   * @param {number} score - Puntaje del guión
   * @param {boolean} isApproved - Si el guión está aprobado
   * @returns {Promise<void>}
   */
  async updateScriptScore(scriptId, score, isApproved) {
    await this.query(
      'UPDATE scripts SET score = $1, is_approved = $2 WHERE id = $3',
      [score, isApproved, scriptId]
    );
    
    logger.success('Score de guión actualizado', { 
      scriptId, 
      score, 
      isApproved 
    });
  }

  /**
   * Guarda una evaluación en la base de datos
   * @param {number} scriptId - ID del guión
   * @param {string} criteria - Criterio de evaluación
   * @param {number} score - Puntaje
   * @param {string} feedback - Comentarios
   * @returns {Promise<object>} - Evaluación guardada
   */
  async saveEvaluation(scriptId, criteria, score, feedback) {
    const result = await this.query(
      'INSERT INTO evaluations (script_id, criteria, score, feedback) VALUES ($1, $2, $3, $4) RETURNING *',
      [scriptId, criteria, score, feedback]
    );
    
    logger.success('Evaluación guardada', { 
      evaluationId: result.rows[0].id,
      scriptId,
      score 
    });
    return result.rows[0];
  }

  /**
   * Obtiene estadísticas de la base de datos
   * @returns {Promise<object>} - Estadísticas
   */
  async getStats() {
    try {
      const topicsResult = await this.query('SELECT COUNT(*) as total FROM topics');
      const scriptsResult = await this.query('SELECT COUNT(*) as total FROM scripts');
      const approvedScriptsResult = await this.query('SELECT COUNT(*) as total FROM scripts WHERE is_approved = true');
      
      const stats = {
        totalTopics: parseInt(topicsResult.rows[0].total),
        totalScripts: parseInt(scriptsResult.rows[0].total),
        approvedScripts: parseInt(approvedScriptsResult.rows[0].total)
      };

      logger.info('Estadísticas obtenidas', stats);
      return stats;
    } catch (error) {
      logger.failure('Error obteniendo estadísticas', { error: error.message });
      throw error;
    }
  }

  /**
   * Cierra la conexión a la base de datos
   * @returns {Promise<void>}
   */
  async close() {
    await this.pool.end();
    logger.info('Conexión a base de datos cerrada');
  }
}

export default DatabaseService;
