import winston from 'winston';

class LoggerService {
  constructor() {
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp({
          format: 'YYYY-MM-DD HH:mm:ss'
        }),
        winston.format.errors({ stack: true }),
        winston.format.json()
      ),
      defaultMeta: { service: 'video-content-generator' },
      transports: [
        // Archivo para errores
        new winston.transports.File({
          filename: 'logs/error.log',
          level: 'error',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json()
          )
        }),
        // Archivo para todos los logs
        new winston.transports.File({
          filename: 'logs/combined.log',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json()
          )
        })
      ],
    });

    // Si no estamos en producción, también log a la consola
    if (process.env.NODE_ENV !== 'production') {
      this.logger.add(new winston.transports.Console({
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.simple(),
          winston.format.printf(({ level, message, timestamp, ...meta }) => {
            return `${timestamp} [${level}]: ${message} ${Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ''}`;
          })
        )
      }));
    }

    this.createLogsDirectory();
  }

  createLogsDirectory() {
    import('fs').then(({ mkdirSync }) => {
      try {
        mkdirSync('logs', { recursive: true });
      } catch (error) {
        // Directorio ya existe
      }
    });
  }

  info(message, meta = {}) {
    this.logger.info(message, meta);
  }

  error(message, meta = {}) {
    this.logger.error(message, meta);
  }

  warn(message, meta = {}) {
    this.logger.warn(message, meta);
  }

  debug(message, meta = {}) {
    this.logger.debug(message, meta);
  }

  // Métodos con emojis para mejor UX en consola
  success(message, meta = {}) {
    this.logger.info(`✅ ${message}`, meta);
  }

  failure(message, meta = {}) {
    this.logger.error(`❌ ${message}`, meta);
  }

  progress(message, meta = {}) {
    this.logger.info(`🔄 ${message}`, meta);
  }

  start(message, meta = {}) {
    this.logger.info(`🚀 ${message}`, meta);
  }
}

export default new LoggerService();
