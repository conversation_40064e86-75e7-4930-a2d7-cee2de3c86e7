import logger from './logger.js';

class MockDatabaseService {
  constructor() {
    this.mockData = {
      topics: [],
      scripts: [],
      evaluations: []
    };
    this.nextId = 1;
  }

  async query(query, params = []) {
    // Simular delay de base de datos
    await new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50));
    
    logger.debug('Ejecutando consulta SQL MOCK', { 
      query: query.substring(0, 100) + '...',
      paramsCount: params.length 
    });

    // Simular resultado exitoso
    return { rowCount: 1, rows: [] };
  }

  async saveTopic(topic) {
    const mockTopic = {
      id: this.nextId++,
      title: topic.title,
      description: topic.description,
      popularity_score: topic.popularity_score,
      is_selected: false,
      created_at: new Date()
    };
    
    this.mockData.topics.push(mockTopic);
    
    logger.success('Tema guardado (MOCK)', { topicId: mockTopic.id, title: topic.title });
    return mockTopic;
  }

  async markTopicsAsSelected(topicIds) {
    for (const topicId of topicIds) {
      const topic = this.mockData.topics.find(t => t.id === topicId);
      if (topic) {
        topic.is_selected = true;
      }
    }
    
    logger.success('Temas marcados como seleccionados (MOCK)', { count: topicIds.length });
  }

  async saveScript(topicId, title, content) {
    const mockScript = {
      id: this.nextId++,
      topic_id: topicId,
      title: title,
      content: content,
      score: null,
      is_approved: false,
      created_at: new Date()
    };
    
    this.mockData.scripts.push(mockScript);
    
    logger.success('Guión guardado (MOCK)', { scriptId: mockScript.id, title });
    return mockScript;
  }

  async updateScriptScore(scriptId, score, isApproved) {
    const script = this.mockData.scripts.find(s => s.id === scriptId);
    if (script) {
      script.score = score;
      script.is_approved = isApproved;
    }
    
    logger.success('Score de guión actualizado (MOCK)', { 
      scriptId, 
      score, 
      isApproved 
    });
  }

  async saveEvaluation(scriptId, criteria, score, feedback) {
    const mockEvaluation = {
      id: this.nextId++,
      script_id: scriptId,
      criteria: criteria,
      score: score,
      feedback: feedback,
      created_at: new Date()
    };
    
    this.mockData.evaluations.push(mockEvaluation);
    
    logger.success('Evaluación guardada (MOCK)', { 
      evaluationId: mockEvaluation.id,
      scriptId,
      score 
    });
    return mockEvaluation;
  }

  async getStats() {
    const stats = {
      totalTopics: this.mockData.topics.length,
      totalScripts: this.mockData.scripts.length,
      approvedScripts: this.mockData.scripts.filter(s => s.is_approved).length
    };

    logger.info('Estadísticas obtenidas (MOCK)', stats);
    return stats;
  }

  async close() {
    logger.info('Conexión a base de datos cerrada (MOCK)');
  }
}

export default MockDatabaseService;
