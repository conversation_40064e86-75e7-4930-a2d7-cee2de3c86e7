import OpenAI from 'openai';
import logger from './loggerService.js';
import { generateTopicsPrompt, selectBestTopicsPrompt } from '../../prompts/topicGeneration.js';
import { generateScriptPrompt } from '../../prompts/scriptGeneration.js';
import { evaluateScriptPrompt } from '../../prompts/scriptEvaluation.js';

class OpenAIService {
  constructor() {
    this.client = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  /**
   * Realiza una llamada a la API de OpenAI
   * @param {string} prompt - El prompt a enviar
   * @param {object} options - Opciones adicionales
   * @returns {Promise<string>} - La respuesta de OpenAI
   */
  async generateCompletion(prompt, options = {}) {
    const defaultOptions = {
      model: "gpt-4o-mini",
      temperature: 0.7,
      max_tokens: 4000,
    };

    const finalOptions = { ...defaultOptions, ...options };

    try {
      logger.progress('Enviando prompt a OpenAI', {
        model: finalOptions.model,
        temperature: finalOptions.temperature,
        promptLength: prompt.length
      });

      const response = await this.client.chat.completions.create({
        model: finalOptions.model,
        messages: [{ role: "user", content: prompt }],
        temperature: finalOptions.temperature,
        max_tokens: finalOptions.max_tokens,
      });

      const content = response.choices[0].message.content;
      
      logger.success('Respuesta recibida de OpenAI', {
        responseLength: content.length,
        tokensUsed: response.usage?.total_tokens || 'unknown'
      });

      return content;
    } catch (error) {
      logger.failure('Error en llamada a OpenAI', {
        error: error.message,
        model: finalOptions.model
      });
      throw error;
    }
  }

  /**
   * Limpia el contenido JSON de bloques de código markdown
   * @param {string} content - Contenido a limpiar
   * @returns {object} - Objeto JSON parseado
   */
  parseJSONResponse(content) {
    try {
      // Limpiar el contenido de bloques de código markdown si existen
      const cleanContent = content
        .replace(/```json\n?/g, '')
        .replace(/```\n?/g, '')
        .trim();
      
      return JSON.parse(cleanContent);
    } catch (error) {
      logger.failure('Error parseando respuesta JSON', {
        error: error.message,
        content: content.substring(0, 200) + '...'
      });
      throw new Error(`Error parseando JSON: ${error.message}`);
    }
  }

  /**
   * Genera temas populares para videos
   * @returns {Promise<object>} - Datos de temas generados
   */
  async generatePopularTopics() {
    const prompt = generateTopicsPrompt();
    const response = await this.generateCompletion(prompt, { temperature: 0.8 });
    return this.parseJSONResponse(response);
  }

  /**
   * Selecciona los mejores temas de una lista
   * @param {Array} topics - Lista de temas
   * @returns {Promise<string>} - Números de temas seleccionados
   */
  async selectBestTopics(topics) {
    const prompt = selectBestTopicsPrompt(topics);
    return await this.generateCompletion(prompt, { temperature: 0.3 });
  }

  /**
   * Genera un guión para un tema específico
   * @param {object} topic - Tema para el cual generar el guión
   * @returns {Promise<string>} - Guión generado
   */
  async generateScript(topic) {
    const prompt = generateScriptPrompt(topic);
    return await this.generateCompletion(prompt, { temperature: 0.7 });
  }

  /**
   * Evalúa un guión de video
   * @param {object} script - Guión a evaluar
   * @returns {Promise<object>} - Evaluación del guión
   */
  async evaluateScript(script) {
    const prompt = evaluateScriptPrompt(script);
    const response = await this.generateCompletion(prompt, { temperature: 0.3 });
    return this.parseJSONResponse(response);
  }
}

export default OpenAIService;
