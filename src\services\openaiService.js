import OpenAI from 'openai';
import logger from '../utils/logger.js';

class OpenAIService {
  constructor() {
    this.client = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  /**
   * Realiza una llamada a la API de OpenAI
   * @param {string} prompt - El prompt a enviar
   * @param {object} options - Opciones adicionales
   * @returns {Promise<string>} - La respuesta de OpenAI
   */
  async generateCompletion(prompt, options = {}) {
    const defaultOptions = {
      model: "gpt-4o-mini",
      temperature: 0.7,
      max_tokens: 4000,
    };

    const finalOptions = { ...defaultOptions, ...options };

    try {
      logger.info('Enviando prompt a OpenAI', { 
        model: finalOptions.model,
        temperature: finalOptions.temperature,
        promptLength: prompt.length 
      });

      const response = await this.client.chat.completions.create({
        model: finalOptions.model,
        messages: [{ role: "user", content: prompt }],
        temperature: finalOptions.temperature,
        max_tokens: finalOptions.max_tokens,
      });

      const content = response.choices[0].message.content;
      
      logger.info('Respuesta recibida de OpenAI', { 
        responseLength: content.length,
        tokensUsed: response.usage?.total_tokens || 'unknown'
      });

      return content;
    } catch (error) {
      logger.error('Error en llamada a OpenAI', { 
        error: error.message,
        model: finalOptions.model 
      });
      throw error;
    }
  }

  /**
   * Limpia el contenido JSON de bloques de código markdown
   * @param {string} content - Contenido a limpiar
   * @returns {object} - Objeto JSON parseado
   */
  parseJSONResponse(content) {
    try {
      // Limpiar el contenido de bloques de código markdown si existen
      const cleanContent = content
        .replace(/```json\n?/g, '')
        .replace(/```\n?/g, '')
        .trim();
      
      return JSON.parse(cleanContent);
    } catch (error) {
      logger.error('Error parseando respuesta JSON', { 
        error: error.message,
        content: content.substring(0, 200) + '...' 
      });
      throw new Error(`Error parseando JSON: ${error.message}`);
    }
  }

  /**
   * Genera temas populares para videos
   * @returns {Promise<object>} - Datos de temas generados
   */
  async generatePopularTopics() {
    const prompt = `
    Genera una lista de 10 temas populares para videos de YouTube que tengan mucho alcance y sean de interés común para mucha gente. 
    Los temas deben ser actuales, virales o de tendencia. 
    
    Responde en formato JSON con la siguiente estructura:
    {
      "topics": [
        {
          "title": "Título del tema",
          "description": "Descripción breve del tema",
          "popularity_score": número del 1 al 100
        }
      ]
    }
    `;

    const response = await this.generateCompletion(prompt, { temperature: 0.8 });
    return this.parseJSONResponse(response);
  }

  /**
   * Selecciona los mejores temas de una lista
   * @param {Array} topics - Lista de temas
   * @returns {Promise<string>} - Números de temas seleccionados
   */
  async selectBestTopics(topics) {
    const topicsText = topics.map((topic, index) => 
      `${index + 1}. ${topic.title} - ${topic.description} (Score: ${topic.popularity_score})`
    ).join('\n');

    const prompt = `
    De la siguiente lista de temas para videos de YouTube, selecciona los 2-3 mejores basándote en:
    - Potencial viral
    - Interés general del público
    - Facilidad para crear contenido atractivo
    
    Lista de temas:
    ${topicsText}
    
    Responde solo con los números de los temas seleccionados separados por comas (ej: 1,3,7)
    `;

    return await this.generateCompletion(prompt, { temperature: 0.3 });
  }

  /**
   * Genera un guión para un tema específico
   * @param {object} topic - Tema para el cual generar el guión
   * @returns {Promise<string>} - Guión generado
   */
  async generateScript(topic) {
    const prompt = `
    Crea un guión detallado para un video de YouTube sobre el tema: "${topic.title}"
    
    Descripción del tema: ${topic.description}
    
    El guión debe incluir:
    - Hook inicial atractivo (primeros 15 segundos)
    - Introducción clara del tema
    - 3-5 puntos principales bien desarrollados
    - Llamadas a la acción durante el video
    - Cierre memorable con call-to-action
    
    El guión debe ser para un video de 8-12 minutos aproximadamente.
    Usa un tono conversacional y atractivo para YouTube.
    `;

    return await this.generateCompletion(prompt, { temperature: 0.7 });
  }

  /**
   * Evalúa un guión de video
   * @param {object} script - Guión a evaluar
   * @returns {Promise<object>} - Evaluación del guión
   */
  async evaluateScript(script) {
    const prompt = `
    Evalúa el siguiente guión de video de YouTube en una escala del 0 al 100 basándote en:
    
    1. Hook inicial (0-20 puntos): ¿Qué tan atractivos son los primeros 15 segundos?
    2. Estructura (0-20 puntos): ¿Está bien organizado y fluye correctamente?
    3. Engagement (0-20 puntos): ¿Mantiene el interés durante todo el video?
    4. Call-to-actions (0-20 puntos): ¿Incluye CTAs efectivos?
    5. Potencial viral (0-20 puntos): ¿Tiene elementos que pueden hacerlo viral?
    
    Guión a evaluar:
    ${script.content}
    
    Responde en formato JSON:
    {
      "total_score": número del 0 al 100,
      "hook_score": número del 0 al 20,
      "structure_score": número del 0 al 20,
      "engagement_score": número del 0 al 20,
      "cta_score": número del 0 al 20,
      "viral_score": número del 0 al 20,
      "feedback": "comentarios detallados sobre fortalezas y debilidades"
    }
    `;

    const response = await this.generateCompletion(prompt, { temperature: 0.3 });
    return this.parseJSONResponse(response);
  }
}

export default OpenAIService;
