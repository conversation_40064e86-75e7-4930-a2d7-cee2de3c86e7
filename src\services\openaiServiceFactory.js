import OpenAIService from './openaiService.js';
import MockOpenAIService from './mockOpenaiService.js';
import logger from './loggerService.js';

/**
 * Factory para crear el servicio de OpenAI apropiado
 * basado en la configuración del entorno
 */
class OpenAIServiceFactory {
  static create() {
    const useMock = process.env.USE_MOCK_OPENAI === 'true';
    
    if (useMock) {
      logger.info('🎭 Usando Mock OpenAI Service para pruebas');
      return new MockOpenAIService();
    } else {
      logger.info('🤖 Usando OpenAI Service real');
      return new OpenAIService();
    }
  }
}

export default OpenAIServiceFactory;
